import { Control, RegisterOptions } from 'react-hook-form';
import { ActivationStates } from '../../../Components/Inputs/ActivationButton/ActivationButton';
import { ActionConfig } from '../../../Engine/models/action.config';
import { BaseControlConfig } from './base.control.config';

export interface TenantApprovalControlConfig extends BaseControlConfig {
  type: 'tenant-approval';
  tenants: {
    name: string;
    label?: string | undefined;
    control?: Control | undefined;
    active?: string;
    client_id?: string;
    sp_id?: string;
  }[];
  tenantsStorePath: string;
  title: string;
  control?: Control;
  divider?: boolean;
  onChange?: (state?: {
    [key: string]: ActivationStates;
  }) => void | ActionConfig[];
  rules?: RegisterOptions;
  submitOnChange?: boolean;
  isStory?: boolean;
}

export interface DynamicActivationControlConfig<
  T extends Record<string, any>,
  U extends { name: string; label: string; control?: Control } & Record<
    string,
    any
  >,
  V extends keyof T,
  N extends keyof T
> extends BaseControlConfig {
  type: 'activation-buttons';
  items: U[];
  itemsStorePath: string;
  title: string;
  control?: Control;
  divider?: boolean;
  onChange?: (state?: { [key: string]: T }) => void | ActionConfig[];
  rules?: RegisterOptions;
  submitOnChange?: boolean;
  isStory?: boolean;
  submit?: (data: any) => void;
  states: readonly T[];
  valueProp: V;
  nameProp: N;
  itemLabelProp: keyof U;
  itemValueProp: keyof U;
  transparentBackground?: boolean;
}
