/* eslint-disable react/jsx-no-useless-fragment */
// external module imports
import Keycloak from 'keycloak-js';
import { clone } from 'ramda';
import {
  lazy,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { FieldValues, RegisterOptions } from 'react-hook-form';
import styled, { useTheme } from 'styled-components';
// internal module imports
import { InteractionBlocker } from '../../Components';
import FormInputText from '../../Components/Accounts/form-input-text/FormInputText';
import { FOTenantApproval } from '../../Components/ActionPanel/FOTenantApproval/FOTenantApproval';
import { ScratchPadChecklist } from '../../Components/ActionPanel/ScratchPadChecklist/ScratchPadChecklist';
import { DocumentCard } from '../../Components/Cards/DocumentCard/DocumentCard';
import { DragAndDrop } from '../../Components/DragAndDrop/DragAndDrop';
import { Icon } from '../../Components/Icons';
import {
  AddFileInput,
  DatepickerInput,
  DropdownSelect,
  FormRadioButtons,
  HeadingTextInput,
  MultiSelectCheckList,
  PlainTextInput,
  Textarea,
  TimePickerV2Input,
} from '../../Components/Inputs';
import { DateAndTimePicker } from '../../Components/Inputs/DateAndTimePicker/DateAndTimePicker';
import { Loader } from '../../Components/Loader/Loader';
import {
  ActionConfig,
  useErrorStore,
  useFieldAccessPermissions,
} from '../../Engine';
import {
  evaluateFormConditionExpression,
  getNestedProperty,
} from '../../Engine/helpers/client-utils';
import { IFormSelect } from '../../models/IFormSelect';
import { TemplateLiteralLogger } from '../../Utilities';
import { validationRegex } from '../../Utilities/validationRegex';
import { DynamicActivationButtons } from '../DynamicActivationButtons/DynamicActivationButtons';
import {
  OpenDropdownList,
  OptionType,
} from '../OpenDropdownList/OpenDropdownList';
import { OperationAreas } from '../OperationAreas';
import { SelectJobImages } from '../SelectJobImages/SelectJobImages';
import { SPListAndMap } from '../SPListAndMap/SPListAndMap';
import { Subscription } from '../Subscription/Subscription';
import { SubscriptionV2 } from '../SubscriptionWithDisclaimers/SubscriptionDisclaimers';
import { UploadDocument } from '../UploadDocument/UploadDocument';
import { FormGroupControl } from './FormComponents';
import { OptionsFetcher } from './OptionsFetcher';
import { InputControlConfig } from './types/input-control.config';

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Generate control log]:',
    enabled: false,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

const inspect = TemplateLiteralLogger.createLog(
  {
    prefix: '🧐[Generate control inspect]:',
    enabled: true,
    options: { style: { color: '#003d8c' } },
  },
  'i'
);

const warn = TemplateLiteralLogger.createLog(
  {
    prefix: '[Generate control warning]:',
    enabled: true,
    options: { style: { color: '#a03f0b' } },
  },
  'warn'
);

// If the control is special, we want to trigger a modal
const onFocusAction: ActionConfig = {
  type: 'clientAction',
  action: 'triggerModal',
  payload: [
    {
      display: true,
      type: 'warning',
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Warning',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
                icon: {
                  type: 'alarm-clock',
                  size: 60,
                  strokeWidth: '1',
                  stroke: '#e5e5e5',
                  style: { paddingLeft: '1rem' },
                },
                iconPosition: 'right',
              },
              {
                text: 'This change is not instant.',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                  style: {
                    paddingTop: '2rem',
                  },
                },
              },
              {
                text: 'Any field with a clock icon will require an approval process',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            gridAutoFlow: 'row',
            justifyItems: 'center',
          },
        },
        {
          component: 'ButtonRow',
          layout: {
            display: 'grid',
            marginTop: '80px',
            justifyItems: 'center',
            width: '75%',
            marginLeft: 'auto',
            marginRight: 'auto',
          },
          props: {
            buttons: [
              {
                btnValue: 'OK',
                onClick: [
                  {
                    type: 'clientAction',
                    action: 'closeModal',
                    payload: [],
                  },
                ],
              },
            ],
          },
        },
      ],
    },
  ],
};

const ControlWrapper = styled.div`
  position: relative;
`;

type Props = {
  ctrl: InputControlConfig;
  name: string;
  $form: any;
  $formState: any;
  $store: any;
  _callClientAction: any;
  fetcher: any;
  control: any;
  errors: any;
  setValue: any;
  getValues: any;
  defaultValues: any;
  getFieldState: any;
  fieldAccess: {
    view: { [key: string]: string[] };
    edit: { [key: string]: string[] };
    special: { [key: string]: string[] };
  };
  isStory?: boolean;
  formBuilderProps: any;
  _keycloak?: Keycloak;
};

/**
 * GenerateControl is a React component that renders various types of form controls based on the provided configuration.
 *
 * @param {Props} props - The properties used to configure the form control.
 * @param {InputControlConfig} props.ctrl - The control configuration object that defines the type and behavior of the form element.
 * @param {string} props.name - The name of the form control.
 * @param {*} props.$form - The form state object.
 * @param {*} props.$formState - The current state of the form.
 * @param {*} props.$store - The application's store object.
 * @param {function} props.callClientAction - Function to trigger client actions.
 * @param {function} props.fetcher - Function to fetch options for select-type controls.
 * @param {*} props.control - The control object from react-hook-form.
 * @param {*} props.errors - Object containing form errors.
 * @param {function} props.setValue - Function to set the value of form fields.
 * @param {function} props.getValues - Function to get the values of form fields.
 * @param {*} props.defaultValues - The default values for the form fields.
 * @param {function} props.getFieldState - Function to get the state of a specific field.
 * @param {object} props.fieldAccess - Object defining access levels for viewing and editing fields.
 * @param {*} props.formBuilderProps - Additional properties for form building.
 * @param {boolean} [props.isStory] - Optional flag indicating if the component is used in a story.
 *
 * @returns {JSX.Element | null} The rendered form control component or null if access is not permitted.
 */
const GenerateControl = ({
  ctrl,
  name,
  $form,
  $formState,
  $store,
  _callClientAction,
  fetcher,
  control,
  errors,
  setValue,
  getValues,
  defaultValues,
  getFieldState,
  fieldAccess,
  formBuilderProps,
  isStory,
  _keycloak,
}: Props) => {
  const { addError } = useErrorStore();

  const rules = useMemo(() => {
    const validation = ctrl?.validation || {};
    // console.log('VALIDATION', { validation });
    const rules = {
      ...validation,
      pattern: validation.pattern
        ? {
            ...validation.pattern,
            value: new RegExp(validation.pattern.value),
          }
        : validation.conditional && validation.conditional.value
        ? ((value?: string) => {
            const conditionalValidation =
              validation?.conditional &&
              evaluateFormConditionExpression(
                validation.conditional.value,
                $store,
                $form,
                $formState
              );
            return {
              message:
                validationRegex[
                  conditionalValidation as keyof typeof validationRegex
                ].message,
              value: new RegExp(
                validationRegex[
                  conditionalValidation as keyof typeof validationRegex
                ].pattern
              ),
            };
          })()
        : {},
      validate: validation.match
        ? {
            value: (value?: string) =>
              value === getValues(validation.match?.value) ||
              validation.match?.message,
          }
        : {},
    } as
      | Omit<
          RegisterOptions<FieldValues, string>,
          'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'
        >
      | undefined;

    return rules;
  }, [ctrl?.validation, $form, $formState, $store, getValues]);

  // Use a ref to track if we've already set the initial value
  const initialValueSet = useRef(false);

  useEffect(() => {
    /**
     * Returns the default value for the control. If the control has a dataTransformMapField
     * property, it will be used to transform the default value. If the default value is an
     * array and the dataTransformMapField is a string, it will be used to extract the value
     * from each item in the array. If the default value is an array and the dataTransformMapField
     * is an array, it will be used to extract the values from each item in the array and
     * return an object with the extracted values. If the default value is not an array, it
     * will be returned as is.
     *
     * The transformation logic is as follows:
     * - If the default value is an array and `dataTransformMapField` is a string, it extracts the value from each item in the array.
     * - If the default value is an array and `dataTransformMapField` is an array, it extracts the specified fields from each item and returns an array of objects.
     * - If the default value is not an array, it returns the value directly.
     *
     * @returns {any} The transformed default value for the control, which can be an array, object, or a single value.
     */
    function getControlDefaultValue() {
      if (!defaultValues || !name || !defaultValues[name]) return undefined;

      if (ctrl.dataTransformMapField) {
        if (Array.isArray(defaultValues[name])) {
          if (typeof ctrl.dataTransformMapField === 'string') {
            return defaultValues[name].map(
              (value: any) =>
                value &&
                ctrl.dataTransformMapField &&
                value[String(ctrl.dataTransformMapField)]
            );
          }
          if (Array.isArray(ctrl.dataTransformMapField)) {
            return defaultValues[name].map(
              (value: any) =>
                value &&
                ctrl.dataTransformMapField &&
                (ctrl.dataTransformMapField as string[]).reduce(
                  (acc: any, field: string) => ({
                    ...acc,
                    [field]: value[field],
                  }),
                  {}
                )
            );
          }
        } else {
          if (typeof ctrl.dataTransformMapField === 'string') {
            return defaultValues[name][ctrl.dataTransformMapField];
          }
          if (Array.isArray(ctrl.dataTransformMapField)) {
            return ctrl.dataTransformMapField.reduce(
              (acc: any, field: string) => ({
                ...acc,
                [field]: defaultValues[name][field],
              }),
              {}
            );
          }
        }
      } else {
        return defaultValues[name];
      }

      return undefined;
    }

    /**
     * Initializes the form control with a default value if certain conditions are met.
     *
     * This function checks if the control's name is defined, if default values are present,
     * and if the control is not dirty (i.e., not modified by the user). If these conditions
     * are satisfied, it retrieves the default value for the control and sets it without marking
     * the field as dirty or touched. Additionally, if a data transformation mapping field is
     * present, it derives a new field name and updates the application state with the derived
     * value and the original value.
     *
     * @param {string} name - The name of the form control.
     * @param {object} defaultValues - The default values for the form controls.
     * @param {function} getFieldState - A function to get the current state of the field.
     * @param {function} getControlDefaultValue - A function to retrieve the default value for the control.
     * @param {function} setValue - A function to set the value of the form control.
     * @param {object} $store - The store object used to manage application state.
     * @param {object} ctrl - The control object containing metadata about the control.
     */
    // Only set the value once when the component mounts or when ctrl changes
    if (
      name &&
      defaultValues &&
      defaultValues[name] &&
      !getFieldState(name).isDirty &&
      !initialValueSet.current
    ) {
      const controlDefaultValue = getControlDefaultValue();
      if (controlDefaultValue !== undefined) {
        setValue(name, controlDefaultValue, {
          shouldDirty: false,
          shouldTouch: false,
        });

        if (ctrl.dataTransformMapField) {
          $store.setState((state: any) => {
            const derivedValue = controlDefaultValue;
            const derivedField = `derived${name[0].toUpperCase()}${name.slice(
              1
            )}`;
            return {
              [derivedField]: derivedValue,
              originalValues: {
                ...state.originalValues,
                [name]: derivedValue,
              },
            };
          });
        }

        initialValueSet.current = true;
      }
    }
  }, [$store, ctrl, defaultValues, getFieldState, name, setValue]);

  useEffect(() => {
    (async () => {
      if (ctrl.events) {
        for (const ev of ctrl.events) {
          const valid = ev.when
            ? evaluateFormConditionExpression(
                ev.when,
                $store,
                $form,
                $formState
              )
            : false;
          if (valid) {
            await _callClientAction(ev.actionHandler);
          }
        }
      }
    })();
  }, [ctrl]);

  const hideControl = useMemo(
    () =>
      ctrl.hiddenWhen
        ? evaluateFormConditionExpression(
            ctrl.hiddenWhen,
            $store,
            $form,
            $formState
          )
        : false,
    [ctrl]
  );
  const disableControl = useMemo(
    () =>
      ctrl.disabledWhen
        ? evaluateFormConditionExpression(
            ctrl.disabledWhen,
            $store,
            $form,
            $formState
          )
        : false,
    [ctrl]
  );

  const dataKey = ctrl?.fieldAccessKey || name || '';
  const { canView, canEdit, isSpecial } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey: dataKey,
    fieldAccessPath: ctrl?.fieldAccessPath,
  });

  const [focusedControls, setFocusedControls] = useState<Set<string>>(
    new Set()
  );
  const [specialFileUploadWarningGiven, setSpecialFileUploadWarningGiven] =
    useState(false);

  const handleFocus = useCallback(
    (controlName: string) => async (ev: any) => {
      console.log(controlName, ': focused!');
      if (!focusedControls.has(controlName)) {
        setFocusedControls((prev) => new Set(prev).add(controlName));
        if (isSpecial) {
          await _callClientAction(onFocusAction);
        }
        // Explicitly set focus on the element
        ev?.target?.focus && ev.target.focus();
      }
    },
    [focusedControls, isSpecial, _callClientAction]
  );

  const commonProps = {
    name,
    control,
    rules,
    error: (errors as any)[name],
    label: ctrl.label || '',
    disabled:
      (ctrl?.fieldAccessPath && (disableControl || !canEdit || !canView)) ||
      disableControl,
    state:
      ctrl?.['state'] ||
      ((disableControl || !canEdit || !canView ? 'display-only' : 'default') as
        | 'display-only'
        | 'default'),
    // icon: isSpecial && <Icon type={''} />,
    onFocus: handleFocus(name),
    _callClientAction,
  };

  // if (isSpecial) {
  //   (commonProps as any).icon = <Icon type="alarm-clock" size={60} strokeWidth="1" color="#e5e5e5" style={{ paddingLeft: '1rem' }} />;
  // }
  const userIsSp = useMemo(
    () => $store?.sp_profile?.id === $store?.my_profile?.sp,
    [$store?.sp_profile?.id, $store?.my_profile?.sp]
  );

  switch (ctrl.type) {
    case 'underlined-text':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <FormInputText type={'text'} {...commonProps} />
            </ControlWrapper>
          )}
        </>
      );
    case 'text-with-heading':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <HeadingTextInput
                type={'text'}
                placeholder={ctrl.placeholder || ''}
                icon={
                  isSpecial ? (
                    <Icon type="alarm-clock" />
                  ) : (
                    ctrl.icon && <Icon type={ctrl.icon} />
                  )
                }
                position={isSpecial && !ctrl.position ? 'right' : ctrl.position}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );

    case 'plain-text':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <PlainTextInput
                type={'text'}
                placeholder={
                  ctrl.placeholder || commonProps.disabled
                    ? defaultValues[ctrl.name]
                    : ''
                }
                icon={
                  isSpecial ? (
                    <Icon type="alarm-clock" />
                  ) : (
                    ctrl.icon && <Icon type={ctrl.icon} />
                  )
                }
                position={isSpecial && !ctrl.position ? 'right' : ctrl.position}
                instructions={ctrl.instructions}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    case 'textarea':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <Textarea
                type={'textarea'}
                placeholder={ctrl.placeholder || ''}
                icon={
                  isSpecial ? (
                    <Icon type="alarm-clock" />
                  ) : (
                    ctrl.icon && <Icon type={ctrl.icon} />
                  )
                }
                position={isSpecial && !ctrl.position ? 'right' : ctrl.position}
                instructions={ctrl.instructions}
                rows={ctrl.rows}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    case 'add-file':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <AddFileInput
                type={'select'}
                placeholder={ctrl.placeholder || ''}
                icon={
                  isSpecial ? (
                    <Icon type="alarm-clock" />
                  ) : (
                    ctrl.icon && <Icon type={ctrl.icon} />
                  )
                }
                position={isSpecial && !ctrl.position ? 'right' : ctrl.position}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    case 'radio-group':
      inspect`${'(Line 623)'} Radio group disabled: ${commonProps.disabled}`;
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              {ctrl.options.source === 'literal' ? (
                <FormRadioButtons
                  data={ctrl.options.data as IFormSelect[]}
                  instructions={ctrl.instructions || ''}
                  returnBoolean={ctrl.returnBoolean || false}
                  size={ctrl.size}
                  columns={ctrl.columns}
                  mobileColumns={ctrl.mobileColumns}
                  mobileBreakpoint={ctrl.mobileBreakpoint}
                  {...commonProps}
                />
              ) : (
                <OptionsFetcher config={ctrl}>
                  {({
                    options,
                    labelProp,
                    valueProp,
                  }: {
                    options: any[];
                    labelProp: string;
                    valueProp: string;
                  }) => (
                    <FormRadioButtons
                      data={
                        options.map((opt) => ({
                          label: opt[labelProp],
                          value: opt[valueProp],
                        })) as IFormSelect[]
                      }
                      instructions={ctrl.instructions || ''}
                      returnBoolean={ctrl.returnBoolean || false}
                      size={ctrl.size}
                      {...commonProps}
                    />
                  )}
                </OptionsFetcher>
              )}
              <InteractionBlocker
                disableCondition={commonProps.disabled}
                label={'read only'}
                icon={{
                  type: 'lock-04',
                  size: 20,
                  strokeWidth: '1',
                  color: '#b28511',
                }}
              />
            </ControlWrapper>
          )}
        </>
      );
    case 'single-select':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              {ctrl.options.source === 'literal' ? (
                <DropdownSelect
                  items={ctrl.options.data || []}
                  multiSelect={false}
                  placeholder={ctrl.placeholder || 'Search items...'}
                  labelProp={ctrl.labelProp}
                  valueProp={ctrl.valueProp}
                  instructions={ctrl.instructions}
                  dropdownScroll={true}
                  className={ctrl.className}
                  onDropdownSelectChange={ctrl.onDropdownSelectChange}
                  icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                  position={
                    isSpecial && !ctrl.position ? 'right' : ctrl.position
                  }
                  store={$store}
                  notSearchable={ctrl.notSearchable}
                  {...commonProps}
                />
              ) : (
                <OptionsFetcher config={ctrl}>
                  {({ options }: { options: any[] }) => (
                    <DropdownSelect
                      items={options}
                      multiSelect={false}
                      placeholder={ctrl.placeholder || 'Search items...'}
                      labelProp={ctrl.labelProp}
                      valueProp={ctrl.valueProp}
                      instructions={ctrl.instructions}
                      className={ctrl.className}
                      dropdownScroll={true}
                      icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                      onDropdownSelectChange={ctrl.onDropdownSelectChange}
                      position={
                        isSpecial && !ctrl.position ? 'right' : ctrl.position
                      }
                      store={$store}
                      notSearchable={ctrl.notSearchable}
                      {...commonProps}
                    />
                  )}
                </OptionsFetcher>
              )}
            </ControlWrapper>
          )}
        </>
      );

    case 'multi-select':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              {ctrl.options.source === 'literal' ? (
                <DropdownSelect
                  items={ctrl.options.data || []}
                  multiSelect={true}
                  placeholder={ctrl.placeholder || 'Search items...'}
                  labelProp={ctrl.labelProp}
                  valueProp={ctrl.valueProp}
                  instructions={ctrl.instructions}
                  className={ctrl.className}
                  dropdownScroll={true}
                  onDropdownSelectChange={ctrl.onDropdownSelectChange}
                  icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                  position={
                    isSpecial && !ctrl.position ? 'right' : ctrl.position
                  }
                  store={$store}
                  notSearchable={ctrl.notSearchable}
                  {...commonProps}
                />
              ) : (
                <OptionsFetcher config={ctrl}>
                  {({ options }: { options: any[] }) => (
                    <DropdownSelect
                      items={options}
                      multiSelect={true}
                      placeholder={ctrl.placeholder || 'Search items...'}
                      labelProp={ctrl.labelProp}
                      valueProp={ctrl.valueProp}
                      instructions={ctrl.instructions}
                      className={ctrl.className}
                      dropdownScroll={true}
                      onDropdownSelectChange={ctrl.onDropdownSelectChange}
                      icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                      position={
                        isSpecial && !ctrl.position ? 'right' : ctrl.position
                      }
                      store={$store}
                      notSearchable={ctrl.notSearchable}
                      {...commonProps}
                    />
                  )}
                </OptionsFetcher>
              )}
            </ControlWrapper>
          )}
        </>
      );

    case 'datepicker':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <DatepickerInput
                mode={ctrl.mode}
                instructions={ctrl.instructions}
                disableFuture={ctrl.disableFuture}
                disablePast={ctrl.disablePast}
                placeholder={ctrl.placeholder}
                disabledDates={ctrl.disabledDates}
                iconPosition={ctrl.iconPosition}
                numberOfMonths={ctrl.numberOfMonths}
                weekendSelectable={ctrl.weekendSelectable}
                icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                selected={ctrl.selected}
                {...commonProps}
                // Pass in the secondary field name if it exists in the control configuration
                secondaryName={ctrl.secondaryName}
                // Wire setSecondaryValue to react-hook-form's setValue (or your own setter)
                setSecondaryValue={(fieldName: string, value: string) => {
                  setValue(fieldName, value);
                }}
              />
              <InteractionBlocker disableCondition={commonProps.disabled} />
            </ControlWrapper>
          )}
        </>
      );
    case 'timepicker':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <TimePickerV2Input
                instructions={ctrl.instructions}
                placeholder={ctrl.placeholder}
                iconPosition={ctrl.iconPosition}
                icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                timeString={ctrl.timeString}
                validMinutes={ctrl.validMinutes}
                isafterhours={ctrl.isafterhours}
                {...commonProps}
                onChange={async (newTime: string) => {
                  setValue(name, newTime);

                  Promise.resolve().then(async () => {
                    const formData = getValues();

                    if (
                      newTime &&
                      newTime !== (ctrl.placeholder || '--:--') &&
                      ctrl.enableOnTimeSelectCalls !== false &&
                      ctrl.onTimeSelect &&
                      Array.isArray(ctrl.onTimeSelect)
                    ) {
                      for (const action of ctrl.onTimeSelect) {
                        if (action.payload) {
                          const payloadStr = JSON.stringify(action.payload);

                          if (payloadStr.includes('{formDataRaw.')) {
                            // Check if any referenced fields have empty values
                            const matches = payloadStr.match(
                              /\{formDataRaw\.([^}]+)\}/g
                            );
                            let missingData = false;

                            if (matches) {
                              for (const match of matches) {
                                const fieldName = match
                                  .replace('{formDataRaw.', '')
                                  .replace('}', '');

                                // If a referenced field is the current time field we're setting,
                                // we should use the new value instead of checking the store
                                if (fieldName === name) {
                                  // Use the new time value for this field reference
                                  continue;
                                }

                                // For other fields, check if they have values
                                if (
                                  !formData[fieldName] ||
                                  formData[fieldName] === ''
                                ) {
                                  warn`Cannot make request yet: ${fieldName} has no value`;
                                  missingData = true;
                                  break;
                                }
                              }
                            }

                            if (missingData) {
                              warn`Cannot make request yet: missing data`;
                              continue;
                            }
                          }
                        }

                        // Directly modify the action payload to use the current newTime value
                        // if it references the current field
                        if (action.payload) {
                          const origPayload = JSON.stringify(action.payload);
                          // Replace any references to the current field with the actual value
                          const updatedPayload = origPayload.replace(
                            new RegExp(`\\{formDataRaw\\.${name}\\}`, 'g'),
                            newTime
                          );

                          // Use the updated payload for the action
                          action.payload = JSON.parse(updatedPayload);
                        }

                        const response = await _callClientAction(action);
                        inspect`Timepicker action response - ${'Line 935'} ${response}`;

                        // Store the response in the store under the control's name if needed
                        if (response) {
                          $store.setState((prevState: any) => ({
                            ...prevState,
                            [name]: response,
                          }));
                        }
                      }
                    }
                  });
                }}
              />
              <InteractionBlocker disableCondition={commonProps.disabled} />
            </ControlWrapper>
          )}
        </>
      );
    case 'date-and-time-picker':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <DateAndTimePicker
                instructions={ctrl.instructions}
                placeholder={ctrl.placeholder}
                iconPosition={ctrl.iconPosition}
                icon={isSpecial ? 'alarm-clock' : ctrl.icon}
                value={ctrl.value}
                validMinutes={ctrl.validMinutes}
                mode={ctrl.mode}
                numberOfMonths={ctrl.numberOfMonths}
                disablePast={ctrl.disablePast}
                disabledDates={ctrl.disabledDates}
                disableFuture={ctrl.disableFuture}
                weekendSelectable={ctrl.weekendSelectable}
                {...commonProps}
              />
              <InteractionBlocker disableCondition={commonProps.disabled} />
            </ControlWrapper>
          )}
        </>
      );
    case 'drag-and-drop':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              {fetcher.state !== 'idle' && <Loader type="alert" />}
              <DragAndDrop
                onDrop={async (acceptedFiles, fileRejections, evt) => {
                  inspect`Inspecting ${'(drag-and-drop'} - ${{
                    acceptedFiles,
                    fileRejections,
                  }}`;
                  if (canEdit && isSpecial && !specialFileUploadWarningGiven) {
                    await _callClientAction(onFocusAction);
                    setSpecialFileUploadWarningGiven(true);
                    return;
                  }
                  const form = new FormData();
                  if (ctrl.documentSrc === 'sp') {
                    form.append('sp_id', String($store?.sp_profile?.id));
                  }
                  if (ctrl.documentSrc === 'staff') {
                    form.append(
                      'staff_id',
                      String($store?.staff_profile?.sso_id)
                    );
                  }
                  // prevent submission if there are file rejections
                  if (fileRejections.length > 0) {
                    return;
                  }
                  form.append('file', acceptedFiles[0]);
                  form.append('purpose', ctrl.purpose);
                  form.append('url', ctrl.url);
                  form.append('list', ctrl.list || '');
                  form.forEach((value, key) => {
                    log`Form data - ${key}: ${value}`;
                  });
                  try {
                    fetcher.submit(form, {
                      method: 'post',
                      action: ctrl.action,
                      encType: 'multipart/form-data',
                    });
                    log`Successful call`;
                  } catch (error) {
                    warn`Failed call - ${'(drag-and-drop)'} ${error}`;
                    addError({
                      key: `drag-and-drop-${Date.now()}`,
                      message:
                        error instanceof Error
                          ? `${error.message}: Please try refreshing the page`
                          : 'Unknown error: Try refreshing the website',
                      source: 'server',
                      stackTrace:
                        error instanceof Error ? error.stack : undefined,
                    });
                  }
                }}
                filename={$form[ctrl.name]?.file?.name || ctrl.emptyText}
                className={ctrl.className}
                fileData={formBuilderProps[ctrl.name]}
                instructions={ctrl.instructions}
                iconLeft={ctrl.iconLeft}
                iconRight={
                  canEdit && isSpecial ? 'alarm-clock' : ctrl.iconRight
                }
                toggleButton={ctrl.toggleButton}
                uploadState={ctrl.uploadState}
                fileTypesAllowed={ctrl.fileTypesAllowed}
                fileSizeLimit={ctrl.fileSizeLimit}
                _onButtonClick={async () => {
                  if (canEdit && isSpecial && !specialFileUploadWarningGiven) {
                    await _callClientAction(onFocusAction);
                    setSpecialFileUploadWarningGiven(true);
                  }
                }}
                _blockUpload={!specialFileUploadWarningGiven}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );

    case 'drag-and-drop-in-memory': {
      const derived = clone(formBuilderProps[ctrl.name]);
      const ref_id = ctrl.pathToRefId
        ? getNestedProperty($store, ctrl.pathToRefId)
        : '';
      if (!$store?.[`derived${ctrl.name}`]) {
        $store.setState((state: any) => ({
          [`derived${ctrl.name}`]: {
            file: '',
            purpose: derived.purpose,
            url: ctrl.url,
            list: ctrl.list,
          },
          originalValues: {
            ...state.originalValues,
            [ctrl.name]: derived,
          },
        }));
      }
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <DragAndDrop
                onDrop={async (acceptedFiles, fileRejections, evt) => {
                  if (canEdit && isSpecial && !specialFileUploadWarningGiven) {
                    await _callClientAction(onFocusAction);
                    setSpecialFileUploadWarningGiven(true);
                    return;
                  }
                  setValue(name, {
                    list: ctrl.list || 'False',
                    purpose: ctrl.purpose,
                    file: acceptedFiles[0] as File,
                    url: ctrl.url,
                    ref_id,
                  });
                }}
                className={ctrl.className}
                filename={
                  ($store &&
                    $store?.formDataRaw &&
                    $store?.formDataRaw[ctrl.name] &&
                    $store?.formDataRaw[ctrl.name]?.file?.name) ||
                  formBuilderProps[ctrl.name]?.filename ||
                  $form[ctrl.name]?.file?.name ||
                  ctrl.emptyText
                }
                fileData={formBuilderProps[ctrl.name]}
                instructions={ctrl.instructions}
                iconLeft={ctrl.iconLeft}
                iconRight={
                  canEdit && isSpecial ? 'alarm-clock' : ctrl.iconRight
                }
                toggleButton={ctrl.toggleButton}
                uploadState={ctrl.uploadState}
                fileTypesAllowed={ctrl.fileTypesAllowed}
                fileSizeLimit={ctrl.fileSizeLimit}
                _onButtonClick={async () => {
                  if (canEdit && isSpecial && !specialFileUploadWarningGiven) {
                    await _callClientAction(onFocusAction);
                    setSpecialFileUploadWarningGiven(true);
                  }
                }}
                _blockUpload={!specialFileUploadWarningGiven}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'upload-document': {
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <UploadDocument
                fileProps={ctrl.fileProps}
                layout={ctrl.layout}
                onDrop={(acceptedFiles, fileRejections, evt) => {
                  // You can add logic to handle file rejections if desired.

                  inspect`UploadDocument onDrop executed`;
                  inspect`Accepted files: ${'Line 1120'} - ${acceptedFiles}`;
                  inspect`File rejections: ${'Line 1129'} - ${fileRejections}`;

                  if (acceptedFiles && acceptedFiles.length > 0) {
                    setValue(name, acceptedFiles);
                  }
                }}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'select-job-images': {
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <SelectJobImages
                items={ctrl.items || []}
                updateFormBuilder={(selectedIds: number[]) => {
                  // Update the form value with the array of selected job image IDs
                  setValue(name, selectedIds);
                }}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'form-group':
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <FormGroupControl
                isStory={isStory}
                getFieldState={getFieldState}
                formConfig={ctrl.formConfig}
                addBtn={ctrl.addBtn}
                _fetcher={fetcher}
                {...commonProps}
              />
              <InteractionBlocker disableCondition={commonProps.disabled} />
            </ControlWrapper>
          )}
        </>
      );

    case 'document-card': {
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              {ctrl.documents && ctrl.documents.length > 0 ? (
                <DocumentCard
                  filename={ctrl.documents[0].filename}
                  created={ctrl.documents[0].created}
                  thumbnail={ctrl.documents[0].thumbnail}
                  purpose={ctrl.purpose}
                  purposeAsName={ctrl.purposeAsName}
                  fileData={formBuilderProps[ctrl.name]}
                  onDocumentChange={(updatedDocument) => {
                    // prevent submission if there are file rejections
                    if (
                      updatedDocument?.file?.size >
                      (ctrl?.fileSizeLimit ?? 5242880)
                    ) {
                      return;
                    }
                    const form = new FormData();
                    form.append('file', updatedDocument.file);
                    form.append('purpose', ctrl.purpose);
                    form.append('url', ctrl.url);
                    form.append('list', ctrl.list || '');
                    try {
                      fetcher.submit(form, {
                        method: 'post',
                        action: ctrl.action,
                        encType: 'multipart/form-data',
                      });
                    } catch (error) {
                      addError({
                        key: `document-card-${Date.now()}`,
                        message:
                          error instanceof Error
                            ? `${error.message}: Please try refreshing the page`
                            : 'Unknown error: Try refreshing the website',
                        source: 'server',
                        stackTrace:
                          error instanceof Error ? error.stack : undefined,
                      });
                    }
                  }}
                  enableUpload={
                    commonProps.disabled ? false : ctrl.enableUpload
                  }
                  isLandscape={ctrl.isLandscape}
                  sp={!userIsSp ? $store?.sp_profile?.id : undefined}
                  fileTypesAllowed={ctrl.fileTypesAllowed}
                  fileSizeLimit={ctrl.fileSizeLimit}
                  isStory={isStory}
                  noDocumentHandler={(purpose: string) => {
                    let documentsNotFound: Array<{
                      purpose: string;
                      exists: boolean;
                    }> = [];
                    if ($store.documentsNotFound) {
                      documentsNotFound = [...$store.documentsNotFound];
                    }
                    $store.setState({
                      documentsNotFound: [
                        ...documentsNotFound,
                        {
                          purpose,
                          exists: false,
                        },
                      ],
                    });
                  }}
                  staff={
                    ctrl?.isOtherStaff
                      ? $store?.staff_profile?.sso_id
                      : undefined
                  }
                  base_url_env_name={ctrl.base_url}
                  _keycloak={_keycloak}
                  _onButtonClick={async () => {
                    if (
                      canEdit &&
                      isSpecial &&
                      !specialFileUploadWarningGiven
                    ) {
                      await _callClientAction(onFocusAction);
                      setSpecialFileUploadWarningGiven(true);
                    }
                  }}
                  _blockUpload={!specialFileUploadWarningGiven}
                  {...commonProps}
                />
              ) : (
                <p>No documents available</p>
              )}
            </ControlWrapper>
          )}
        </>
      );
    }
    case 'multiselect-checklist': {
      const checked = ctrl?.checkedItemsTransformPath
        ? ctrl.checkedItems &&
          getNestedProperty($store, ctrl.checkedItems).map(
            (item: any) =>
              ctrl?.checkedItemsTransformPath &&
              item?.[ctrl.checkedItemsTransformPath]
          )
        : ctrl.checkedItems && getNestedProperty($store, ctrl.checkedItems);
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              {ctrl.options.source === 'literal' ? (
                <MultiSelectCheckList
                  items={ctrl.options?.data || []}
                  heading={ctrl.label || ''}
                  subtext={ctrl.instructions || ''}
                  checkedItems={checked || []}
                  onChange={(selectedItems) => {
                    setValue(name, selectedItems);
                  }}
                  maxColumns={ctrl.maxColumns ?? 3}
                  valueProp={ctrl.valueProp || 'id'}
                  labelProp={ctrl.labelProp || 'name'}
                  hideBorder={ctrl.hideBorder}
                  {...commonProps}
                />
              ) : (
                <OptionsFetcher config={ctrl}>
                  {({ options }: { options: any[] }) => (
                    <MultiSelectCheckList
                      items={options}
                      heading={ctrl.label || ''}
                      subtext={ctrl.instructions || ''}
                      checkedItems={checked || []}
                      onChange={(selectedItems) => {
                        setValue(name, selectedItems);
                      }}
                      maxColumns={ctrl.maxColumns ?? 3}
                      valueProp={ctrl.valueProp || 'id'}
                      labelProp={ctrl.labelProp || 'name'}
                      hideBorder={ctrl.hideBorder}
                      {...commonProps}
                    />
                  )}
                </OptionsFetcher>
              )}
              <InteractionBlocker
                disableCondition={commonProps.disabled}
                label={'read only'}
                icon={{
                  type: 'lock-04',
                  size: 20,
                  strokeWidth: '1',
                  color: `#b28511`,
                }}
              />
            </ControlWrapper>
          )}
        </>
      );
    }
    case 'radius': {
      const derivedOperationalArea = clone(
        $store?.sp_profile?.operational_area
      );
      if (!$store.derivedOperationalArea) {
        $store.setState((state: any) => ({
          derivedOperationalArea,
          originalValues: {
            ...state.originalValues,
            operational_area: derivedOperationalArea,
          },
        }));
      }
      return (
        <>
          {!ctrl.hiddenWhen && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <OperationAreas
                operational_area={formBuilderProps[ctrl.name]}
                heading={ctrl.label || 'Operation Areas'}
                subHeading={ctrl.instructions}
                toolTip={ctrl.toolTip}
                setOperationalArea={(value: any) => {
                  setValue(name, value);
                }}
                marks={ctrl?.marks}
                interactable={!commonProps.disabled}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'open-dropdown': {
      const dropdownOptions = Array.isArray(ctrl.options)
        ? (ctrl.options as OptionType[])
        : [];

      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <OpenDropdownList
                options={dropdownOptions}
                placeholder={ctrl.placeholder || 'Select an option'}
                labelKey={ctrl.labelKey}
                getOptionLabel={ctrl.getOptionLabel}
                width={ctrl.width || '100%'}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'tenant-approval': {
      const _tenants = getNestedProperty($store, ctrl.tenantsStorePath);
      const companies = $store?.sp_enums.companies;
      const tenants =
        !!_tenants && Array.isArray(_tenants) && _tenants.length > 0
          ? _tenants?.map(
              (tenant: {
                active: string;
                client_id: string;
                sp_id: string;
              }) => {
                const company = companies?.find(
                  (org: any) => org.id === tenant.client_id
                );
                return {
                  ...tenant,
                  label: company?.name ?? '',
                  name: `tenant_approval_${company?.short_name}`,
                  control: ctrl.control,
                  tenant_id: tenant.client_id,
                };
              }
            )
          : !!ctrl.tenants &&
            Array.isArray(ctrl.tenants) &&
            ctrl.tenants.length > 0
          ? ctrl.tenants.map((tenant) => ({
              ...tenant,
              label: '',
              active: '',
              client_id: '',
              sp_id: '',
              control: ctrl.control,
              tenant_id: tenant.name,
            }))
          : [];
      return (
        <>
          {!ctrl.hiddenWhen && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl.css?.wrapper}
            >
              <FOTenantApproval
                title={ctrl.title}
                tenants={tenants}
                onChange={ctrl.onChange}
                divider={ctrl.divider}
                submitOnChange={ctrl.submitOnChange}
                isStory={ctrl.isStory}
                store={$store}
                keycloak={_keycloak}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'checklist': {
      const items = ctrl.items.map((item) => ({
        ...item,
        control: ctrl.control,
      }));
      return (
        <>
          {!ctrl.hiddenWhen && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl.css?.wrapper}
            >
              <ScratchPadChecklist
                title={ctrl.title}
                items={items}
                submitOnChange={ctrl.submitOnChange}
                isStory={ctrl.isStory}
                store={$store}
                onInvalid={ctrl.onInvalid}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'subscription-agreement': {
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <Subscription
                checked={ctrl?.checked}
                canChange={ctrl?.canChange}
                onChange={(checked) => {
                  $store.setState((state: any) => ({
                    postData: {
                      ...state.postData,
                      details: {
                        additional: {
                          ...state?.sp_profile?.details?.additional,
                          subscriptionData: {
                            ...state?.sp_profile?.details?.additional
                              ?.subscriptionData,
                            [name]: checked,
                          },
                        },
                      },
                    },
                  }));
                }}
                isStory={ctrl.isStory}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'subscription-with-disclaimers-agreement': {
      return (
        <>
          {!hideControl && (
            <ControlWrapper
              data-testid="control-wrapper"
              style={ctrl?.css?.wrapper}
            >
              <SubscriptionV2
                checked={ctrl?.checked}
                canChange={ctrl?.canChange}
                onChange={(checked) => {
                  $store.setState((state: any) => ({
                    postData: {
                      ...state.postData,
                      details: {
                        additional: {
                          ...state?.sp_profile?.details?.additional,
                          subscriptionData: {
                            ...state?.sp_profile?.details?.additional
                              ?.subscriptionData,
                            [name]: checked,
                          },
                        },
                      },
                    },
                  }));
                }}
                isStory={ctrl.isStory}
                {...commonProps}
              />
            </ControlWrapper>
          )}
        </>
      );
    }

    case 'activation-buttons': {
      if (
        (!ctrl.items &&
          !Array.isArray(ctrl.items) &&
          (ctrl.items as any[])?.length === 0) ||
        (!ctrl.states &&
          !Array.isArray(ctrl.states) &&
          (ctrl.states as any[])?.length === 0)
      )
        return (
          <>
            No items to populate, items and states required to render component
          </>
        );
      const items =
        ctrl?.items &&
        ctrl?.items.map &&
        (ctrl?.items || [])?.map((item) => ({
          ...item,
          control: ctrl.control,
        }));
      return (
        <>
          {!ctrl.hiddenWhen &&
            items &&
            Array.isArray(items) &&
            items.length > 0 && (
              <ControlWrapper
                data-testid="control-wrapper"
                style={ctrl.css?.wrapper}
              >
                <DynamicActivationButtons
                  title={ctrl.title}
                  items={items}
                  divider={ctrl.divider}
                  onChange={ctrl.onChange}
                  submitOnChange={ctrl.submitOnChange}
                  isStory={ctrl.isStory}
                  store={$store}
                  keycloakProp={_keycloak}
                  states={ctrl.states}
                  valueProp={ctrl.valueProp}
                  nameProp={ctrl.nameProp}
                  submit={ctrl.submit}
                  itemLabelProp={ctrl.itemLabelProp || 'label'}
                  itemValueProp={ctrl.itemValueProp || 'value'}
                  transparentBackground={ctrl.transparentBackground}
                  {...commonProps}
                />
              </ControlWrapper>
            )}
        </>
      );
    }

    default:
      return null;
  }
};

export default GenerateControl;
