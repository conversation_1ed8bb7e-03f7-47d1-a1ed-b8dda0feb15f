import { ComponentPropsWithoutRef, useMemo } from 'react';
import styled from 'styled-components';
import { StaffMember } from '../../Auth';
import { WorkflowClaimAndJobCard } from '../../Components/Cards/WorkflowClaimAndJobCard';
import { ScrollableContent } from '../../Components/Scrollbar/Scrollbar';
import { AllInfo, useAllInfoUtilities } from '../../Engine';
import { ClaimCard } from '../../Components/Cards/ClaimCard/ClaimCard';

type ClaimCardListProps = {
  scrollable?: boolean;
  claims?: ComponentPropsWithoutRef<typeof WorkflowClaimAndJobCard>['claim'][];
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  displayLogo?: boolean;
  getClaimMenuItems: (
    claim: any
  ) => { icon: string; label: string; path: string }[];
  getJobMenuItems: (
    job: any
  ) => { icon: string; label: string; path: string }[];
  staffMember: StaffMember;
  allInfo: Partial<AllInfo>;
  tokenPrefix?: string;
  token?: string;
};

const Container = styled.div<
  ComponentPropsWithoutRef<'div'> & { scrollable?: boolean }
>`
  width: ${(props) => (props.scrollable ? 'calc(100% - 1rem)' : '100%')};
  gap: ${(props) => props.theme.GapMd};
  display: grid;
  grid-auto-flow: row;
  height: 100%;
  // flex-direction: column;
  // overflow: hidden;
`;

/**
 * Renders a list of claim cards either in a scrollable container or a regular container based on the scrollable flag.
 * @param {ClaimCardListProps} items - The list of claim data to display.
 * @param {boolean} scrollable - Flag indicating whether the list should be scrollable.
 * @returns {JSX.Element} The rendered list of claim cards.
 */

export function ClaimCardList({
  claims = [],
  scrollable = false,
  ClaimLinkRouter,
  JobLinkRouter,
  displayLogo,
  getClaimMenuItems,
  getJobMenuItems,
  staffMember,
  allInfo = {},
  tokenPrefix,
  token,
}: ClaimCardListProps) {
  const {
    displayStateText,
    hasPermission,
    skillsMap,
    formatDate,
    appointmentTypesMap,
    getAppointmentTime,
  } = useAllInfoUtilities({ staffMember, allInfo });

  const mappedClaims = useMemo(() => {
    return claims?.map((claim) => ({
      ...claim,
      //
      stateTextDisplay: displayStateText(claim?.state),
      customer: `${claim?.applicant?.first_name} ${claim?.applicant?.surname}`,
      permissionGranted: hasPermission(claim?.state),
      // skillName: skillsMap[claim?.skill]?.name,
      formattedDate: formatDate(claim?.application_date),
      // appointmentType: appointmentTypesMap[claim?.appointment?.appointment_type]?.name,
      // appointmentTime: getAppointmentTime(claim),

      jobs:
        claim?.jobs?.map((job: any) => ({
          ...job,
          //
          stateTextDisplay: displayStateText(job?.state),
          customer: `${job?.claim?.applicant?.first_name} ${job?.claim?.applicant?.surname}`,
          permissionGranted: hasPermission(job?.state),
          skillName: skillsMap[job?.skill]?.name,
          formattedDate: formatDate(job?.appointment?.range_start),
          appointmentType:
            appointmentTypesMap[job?.appointment?.appointment_type]?.name,
          appointmentTime: getAppointmentTime(job),
        })) || [],

        // Meta for Filters
      filters: {
        states: [
          claim?.state,
          ...claim?.jobs?.map((job: any) => job?.state),
        ],
        value: claim?.jobs[0]?.claim_value || 0,
      },
    }));
  }, [
    claims,
    displayStateText,
    hasPermission,
    skillsMap,
    formatDate,
    appointmentTypesMap,
    getAppointmentTime,
  ]);

  console.log({mappedClaims});

  return (
    <ScrollableContent style={{ height: 'calc(100% - 176px', width: '100%' }}>
      <Container scrollable={scrollable}>
        {mappedClaims?.map((claim) => (
          <ClaimCard
            key={claim.id}
            claim={claim}
            ClaimLinkRouter={ClaimLinkRouter}
            JobLinkRouter={JobLinkRouter}
            displayLogo={displayLogo}
            menuItems={[]}
          />
        ))}
      </Container>
    </ScrollableContent>
  );
}
