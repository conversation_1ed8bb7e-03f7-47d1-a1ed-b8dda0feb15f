import React, { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { UploadDocument } from '../UploadDocument';
import { FormBuilder } from '../form-builder';
import { PDFPreview } from '../PDFviewer';
import styled from 'styled-components';

const RemoveFileButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 32px;
`;

const RemoveFileButton = styled.button`
  background: #222;
  border: 1px solid #e53935;
  color: #e53935;
  padding: 8px 20px 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
`;

export const UploadAndPreview: React.FC = () => {
  const [uploadedFile, setUploadedFile] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const methods = useForm({ defaultValues: { uploadedFile: [] } });

  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      const fileUrl = URL.createObjectURL(files[0]);
      setUploadedFile(fileUrl);
      setShowPreview(true);
    }
  };

  if (!showPreview) {
    return (
      <FormProvider {...methods}>
        <div style={{ maxWidth: 400, margin: '0 auto', padding: 32 }}>
          <UploadDocument
            fileProps={[
              {
                dragAndDropMessage: 'Upload Document',
                onSelect: (files: FileList) => {
                  if (files.length > 0) {
                    handleFileUpload(Array.from(files));
                  }
                },
              },
            ]}
            name="uploadedFile"
            control={methods.control as any}
            onDrop={(acceptedFiles) => {
              handleFileUpload(acceptedFiles);
            }}
          />
        </div>
      </FormProvider>
    );
  }

  return (
    <FormProvider {...methods}>
      <div style={{ background: '#111', padding: 32 }}>
        <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: 0, alignItems: 'stretch', height: '70vh', minHeight: 500, maxHeight: 700 }}>
          <div style={{ background: '#222', minWidth: 0, overflow: 'hidden', position: 'relative', width: '100%', height: '100%', padding: 0, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>
            <div style={{ width: '100%', height: '100%' }}>
              <PDFPreview
                documents={[
                  {
                    documentUrl: uploadedFile || '',
                    isBase64: false,
                  },
                ]}
              />
            </div>
            <RemoveFileButtonContainer>
              <RemoveFileButton
                onClick={() => {
                  setUploadedFile(null);
                  setShowPreview(false);
                }}
              >
                Remove File
              </RemoveFileButton>
            </RemoveFileButtonContainer>
          </div>
          <div style={{ background: '#181818', padding: 32, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', height: '100%' }}>
            <FormBuilder
              config={{
                style: {},
                controls: [
                  {
                    type: 'plain-text',
                    name: 'invoice_amount',
                    label: 'Enter Invoice Amount',
                    css: { wrapper: { width: '100%', marginBottom: 24 } },
                  },
                  {
                    type: 'plain-text',
                    name: 'invoice_number',
                    label: 'Enter Invoice Number',
                    css: { wrapper: { width: '100%', marginBottom: 24 } },
                  },
                  {
                    type: 'plain-text',
                    name: 'confirm_excess',
                    label: 'Confirm Excess',
                    css: { wrapper: { width: '100%' } },
                  },
                ],
              }}
              defaultValues={{ invoice_amount: '', invoice_number: '', confirm_excess: '' }}
              isStory
            />
          </div>
        </div>
        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', padding: '24px 0 0 0', background: '#111', borderTop: '1px solid #222' }}>
          <button style={{ background: 'none', border: '1px solid #222', color: '#444', padding: '12px 24px', borderRadius: 4, fontSize: 16, cursor: 'not-allowed', opacity: 0.5 }} disabled>UPLOAD DOCUMENT</button>
        </div>
      </div>
    </FormProvider>
  );
};