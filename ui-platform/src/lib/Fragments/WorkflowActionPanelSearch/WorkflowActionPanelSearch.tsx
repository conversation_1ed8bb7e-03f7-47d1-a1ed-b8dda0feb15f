import styled from "styled-components";
import { SearchInput } from "../../Components/Inputs/SearchInput/SearchInput";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Switch } from "../../Components/Inputs/Switch/Switch";
import { useFiltersAndSearchStore } from "../../store/useFiltersAndSearchStore";
import { getFilteredData } from "../../Engine";

const PanelContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  border-radius: 10px;
`;

// const Title = styled.h2`
//   color: #fff;
//   font-size: 24px;
//   font-weight: 600;
//   // text-align: center;
//   margin: 0 0 12px 0;
// `;

// const TitleUnderlineContainer = styled.div`
//   display: flex;
//   flex-direction: column;
//   // align-items: center;
//   // width: 100%;
//   align-items: center;
//   margin-bottom: 0;
//   max-width: 320px;
// `;

// const Underline = styled.div`
//   width: 270px;
//   height: 2px;
//   background: #2e8bb8;
//   margin: 0 0 24px 0;
//   border-radius: 2px;
// `;

const SearchRow = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
  margin-bottom: 16px;
  padding: 0 16px;
`;

const WorkflowSearch = styled(SearchInput)`
  width: 90%;
box-sizing: border-box;
  border: 1px solid #6c757d;
  color: #fff;
  font-size: 16px;
  padding-right: 36px;
  margin-left: 5px;
`;

const SwitchRow = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 0;
  width: 100%;
  padding: 0 16px;
`;

const SwitchLabel = styled.span`
  color: #bfc3c5;
  font-size: 15px;
  margin-left: 12px;
  user-select: none;
  padding-top: 6px;
`;

export function WorkflowActionPanelSearch() {
  // Use the Zustand store directly
  const store = useFiltersAndSearchStore();
  const { searchUrl, items, token, tokenPrefix, filterConditions } = store;
  
  // Local component state
  const [isClosedClaim, setIsClosedClaim] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Handle search term changes
  const handleSearchTermChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newTerm = e.target.value;
    setSearchTerm(newTerm);
    
    // If search term is cleared, also clear filters
    if (newTerm === '' && filterConditions.length > 0) {
      store.clearFilters();
      // Reset to original items if search term is cleared
      store.setCurrentItems(items);
    }
  }, [store, filterConditions.length, items]);



  // const handleIncludeClosedClaims = (value: boolean) => {
  //   setIsClosedClaim(value);
  //   // performSearch(searchTerm);
  // };

  // Prevent event propagation for all interactions within the panel
  const handlePanelInteraction = (event: React.MouseEvent | React.KeyboardEvent) => {
    event.stopPropagation();
  };

    // Use the store's performSearch function but wrap it to handle local UI state
    const performSearch = useCallback(async (term: string, isClosedClaim: boolean) => {
      setIsSearching(true);
      try {
        // Use the store's performSearch function
        await store.performSearch(term, isClosedClaim);
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        setIsSearching(false);
      }
    }, [store]);

    return (
        <PanelContainer onClick={handlePanelInteraction} onKeyDown={handlePanelInteraction}>
          {/* <TitleUnderlineContainer>
            <Title>Search</Title>
            <Underline />
          </TitleUnderlineContainer> */}
          <SearchRow>
            <WorkflowSearch
              placeholder="Search"
              value={searchTerm}
              onChange={handleSearchTermChange}
              onSubmit={(e) => {
                e.preventDefault();
                performSearch(searchTerm, isClosedClaim);
              }}
              onClear={() => {
                setSearchTerm('');
                performSearch('', isClosedClaim);
              }}
              isLoading={isSearching}
            />
          </SearchRow>
          <SwitchRow>
            <Switch
              enabled="On"
              disabled="Off"
              name="isClosedClaim"
              isOn={isClosedClaim}
              onChange={setIsClosedClaim}
            />
            <SwitchLabel>Include closed claims</SwitchLabel>
          </SwitchRow>
        </PanelContainer>
    );
}