import React, { useState, useEffect } from "react";
import styled, { keyframes } from "styled-components";
import { FilterCondition } from "../../Engine/models/filter-condition";
import { useFiltersAndSearchStore } from "../../store/useFiltersAndSearchStore";

const slideDown = keyframes`
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideUp = keyframes`
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
`;

const StyledWorkflowActionPanelBuckets = styled.div`
  margin: unset;
  padding-top: 0.1rem;
  width: 100%;
  overflow: hidden;
`;

// const BucketHeader = styled.div`
//   font-size: 24px;
//   font-weight: 500;
//   color: #fff;
//   margin-bottom: 16px;
//   text-align: center;
// `;

// const Underline = styled.div`
//   width: 350px;
//   height: 2px;
//   background: #2e8bb8;
//   margin: 8px auto 24px auto;
//   border-radius: 2px;
// `;

const BucketContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 90%;
  justify-self: center;
`;

const BucketItem = styled.div<{ isActive?: boolean }>`
  // background-color: ${({ isActive }) => (isActive ? '#4299e1' : '#414141')};
  border: 1px solid ${({ isActive }) => (isActive ? '#63b3ed' : '#4a5568')};
  border-radius: 6px;
  padding: 10px 12px;
  color: #fff;
  border: 1px solid #bfc6cc;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: ${({ isActive }) => (isActive ? '#3182ce' : '#4a5568')};
  }
`;

const CurrentBucketSection = styled.div<{ isVisible: boolean }>`
  animation: ${({ isVisible }) => (isVisible ? slideDown : slideUp)} 0.3s ease-in-out forwards;
  display: flex;
  flex-direction: column;
  align-items: center;
  // gap: 12px;
  margin-top: 16px;
  padding: 12px;
  border-radius: 4px;
`;

const CurrentBucketLabel = styled.div`
  font-size: 18px;
  color: #ecc94b;
  font-weight: 500;
`;

const CurrentBucketValue = styled.div`
  font-size: 18px;
  color: #fff;
  font-weight: 500;
`;

const ClearButton = styled.button`
  background-color: #1a202c;
  color: #ecc94b;
  border: 1px solid #bfc6cc;
  // border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #2d3748;
  }
`;

const CurrentBucketDisplay = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  padding-bottom: 50px;
`;

export interface BucketItem {
  id: string;
  name: string;
  filterCondition: FilterCondition;
}

export interface WorkflowActionPanelBucketsProps {
  buckets: BucketItem[];
  label?: string;
  onBucketSelect?: (bucket: BucketItem) => void;
  onClearBuckets?: () => void;
  defaultSelectedBucketId?: string;
  _onNotify?: Function;
}

export function WorkflowActionPanelBuckets({
  buckets = [],
  label = "Bucket Filter",
  onBucketSelect,
  onClearBuckets,
  defaultSelectedBucketId,
  _onNotify
}: WorkflowActionPanelBucketsProps) {
  const { addFilterCondition, clearFilters } = useFiltersAndSearchStore();
  const [selectedBucket, setSelectedBucket] = useState<BucketItem | null>(null);
  const [isCurrentBucketVisible, setIsCurrentBucketVisible] = useState<boolean>(false);

  useEffect(() => {
    if (defaultSelectedBucketId && buckets.length > 0) {
      const defaultBucket = buckets.find(bucket => bucket.id === defaultSelectedBucketId);
      if (defaultBucket) {
        setSelectedBucket(defaultBucket);
        setIsCurrentBucketVisible(true);
      }
    }
  }, [defaultSelectedBucketId, buckets]);

  const handleBucketClick = (bucket: BucketItem) => {
    setSelectedBucket(bucket);
    setIsCurrentBucketVisible(true);
    addFilterCondition(bucket.filterCondition);
    if (onBucketSelect) {
      onBucketSelect(bucket);
    }
    
    // if (_onNotify) {
    //   _onNotify({
    //     selectedBucket: bucket
    //   });
    // }
  };

  const handleClearBuckets = () => {
    setSelectedBucket(null);
    setIsCurrentBucketVisible(false);
    clearFilters();    
    if (onClearBuckets) {
      onClearBuckets();
    }
    
    // if (_onNotify) {
    //   _onNotify({
    //     selectedBucket: null
    //   });
    // }
  };

  return (
    <StyledWorkflowActionPanelBuckets>
      {/* <BucketHeader>{label}</BucketHeader>
      <Underline /> */}
      
      {!selectedBucket && (
        <BucketContainer>
          {buckets.map((bucket) => (
            <BucketItem 
              key={bucket.id}
            //   isActive={selectedBucket?.id === bucket.id}
              onClick={() => handleBucketClick(bucket)}
            >
              {bucket.name}
            </BucketItem>
          ))}
        </BucketContainer>
      )}
      
      {selectedBucket && (
        <CurrentBucketSection isVisible={isCurrentBucketVisible}>
          <CurrentBucketDisplay>
            <CurrentBucketLabel>Current bucket:</CurrentBucketLabel>
            <CurrentBucketValue>{selectedBucket.name}</CurrentBucketValue>
          </CurrentBucketDisplay>
          <ClearButton onClick={handleClearBuckets}>CLEAR BUCKETS</ClearButton>
        </CurrentBucketSection>
      )}
    </StyledWorkflowActionPanelBuckets>
  );
}