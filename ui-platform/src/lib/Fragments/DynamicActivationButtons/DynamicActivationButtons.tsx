import Keycloak from 'keycloak-js';
import _ from 'lodash';
import { ComponentPropsWithoutRef, useEffect, useMemo } from 'react';
import { Control, Controller, useFormContext } from 'react-hook-form';
import styled from 'styled-components';
import { ScratchPadWrapper } from '../../Components/ActionPanel/ScratchPadWrapper/ScratchPadWrapper';
import { DynamicActivationButton as ActivationButton } from '../../Components/Inputs';
import { ActionConfig, useAsyncLoaderStore } from '../../Engine';

const Container = styled.div`
  padding: ${(props) => props.theme.SpacingSm};
`;

type Props<
  T extends Record<string, any>,
  U extends { name: string; label: string; control?: Control } & Record<
    string,
    any
  >,
  V extends keyof T,
  N extends keyof T
> = {
  title: string;
  items: U[];
  itemLabelProp: keyof U;
  itemValueProp: keyof U;
  states: readonly T[];
  valueProp: V;
  nameProp: N;
  divider?: boolean;
  onChange?: (state?: { [key: string]: T }) => void | ActionConfig[];
  submitOnChange?: boolean;
  isStory?: boolean;
  store?: any;
  keycloakProp?: Keycloak;
  _callClientAction?: (config: ActionConfig[]) => void;
  submit?: (data?: any) => void;
  transparentBackground?: boolean;
};

/**
 * DynamicActivationButtons is a flexible, generic React component for rendering a list of activation buttons,
 * each representing an item (such as a tenant, environment, or status) with selectable states.
 *
 * The component is designed for use with React Hook Form and supports advanced scenarios such as
 * server-side actions, custom state management, and Storybook integration.
 *
 * ## Features
 * - Renders a list of activation buttons, each managed as a form field.
 * - Integrates with React Hook Form via Controller for each item.
 * - Supports custom item and state structures via generic props.
 * - Can trigger callbacks, server actions, or form submissions on state change.
 * - Optionally integrates with a store and Keycloak for advanced use cases.
 * - Customizable UI (divider, transparent background, etc.).
 *
 * ## Props
 * @template T - The type describing each possible state (e.g., { value: string, label: string }).
 * @template U - The type describing each item (must include at least { name: string, label: string, control?: Control }).
 * @template V - The key in T used as the value for state selection.
 * @template N - The key in T used as the label for state selection.
 *
 * @param {string} title - Title for the group of activation buttons.
 * @param {U[]} items - Array of item objects, each representing a button (e.g., tenants, environments).
 * @param {keyof U} itemLabelProp - The property name in each item to use as the button label.
 * @param {keyof U} itemValueProp - The property name in each item to use as the button value.
 * @param {readonly T[]} states - Array of possible states for each button (e.g., [{ value: 'active', label: 'Active' }]).
 * @param {V} valueProp - The property name in each state object to use as the value.
 * @param {N} nameProp - The property name in each state object to use as the label.
 * @param {boolean} [divider=true] - Whether to show a divider between buttons.
 * @param {(state?: { [key: string]: T }) => void | ActionConfig[]} [onChange] - Callback when any button's state changes.
 * @param {boolean} [submitOnChange] - If true, calls the submit function on every state change.
 * @param {boolean} [isStory] - If true, indicates Storybook usage.
 * @param {any} [store] - Optional store object for advanced state management.
 * @param {Keycloak} [keycloakProp] - Optional Keycloak instance for authentication scenarios.
 * @param {(config: ActionConfig[]) => void} [_callClientAction] - Optional callback for triggering client/server actions.
 * @param {(data?: any) => void} [submit] - Optional callback for submitting data.
 * @param {boolean} [transparentBackground] - If true, makes the background transparent.
 *
 * @returns {JSX.Element} A group of activation buttons, each managed as a form field and supporting custom state logic.
 *
 * @example
 * <DynamicActivationButtons
 *   title="Tenant Activation"
 *   items={[
 *     { name: 'tenantA', label: 'Tenant A', control, ... },
 *     { name: 'tenantB', label: 'Tenant B', control, ... },
 *   ]}
 *   itemLabelProp="label"
 *   itemValueProp="value"
 *   states={[{ value: 'active', label: 'Active' }, { value: 'inactive', label: 'Inactive' }]}
 *   valueProp="value"
 *   nameProp="label"
 *   onChange={(updatedStates) => { ... }}
 *   submitOnChange={true}
 * />
 */
export function DynamicActivationButtons<
  T extends Record<string, any>,
  U extends { name: string; label: string; control?: Control } & Record<
    string,
    any
  >,
  V extends keyof T,
  N extends keyof T
>({
  title,
  items,
  itemLabelProp,
  itemValueProp,
  divider = true,
  onChange: onChangeEventHandler,
  submitOnChange,
  isStory = false,
  store,
  keycloakProp,
  states,
  valueProp,
  nameProp,
  _callClientAction,
  submit,
  transparentBackground,
}: Props<T, U, V, N>) {
  const { getValues, setValue } = useFormContext();
  const handleChange = async (
    item: U,
    state: T,
    onSelectState: (state?: { [key: string]: T[V] }) => void,
    value: { [key: string]: T[V] }
  ) => {
    console.log({ item, state, value });
    const itemName = item.name;
    const itemStatuses = value;
    const newStatus = state;

    const updatedStatuses = {
      ...itemStatuses,
      [itemName]: state[valueProp],
    };

    if (onSelectState) {
      if (typeof onSelectState === 'function') {
        onSelectState(updatedStatuses);
      }
      if (typeof onChangeEventHandler === 'function') {
        onChangeEventHandler(updatedStatuses);
      }
      if (
        _callClientAction &&
        typeof onChangeEventHandler !== 'function' &&
        Array.isArray(onChangeEventHandler)
      ) {
        // const setState = store?.setState ? store.setState : undefined;
        // if (!setState) return;
        // setState((state: any) => {
        //   type ClientSubscriptionUpdatePayload = {
        //     sp_id: string;
        //     client_id: string;
        //     active: number;
        //   };
        //   const clientSubscriptionUpdates: ClientSubscriptionUpdatePayload[] = [
        //     ...(state?.['client_subscription_updates'] || []),
        //     {
        //       sp_id: store?.sp_profile?.details?.id,
        //       client_id: item.tenant_id,
        //       active: newStatus,
        //     },
        //   ];
        //   return {
        //     client_subscription_updates: clientSubscriptionUpdates,
        //     currentItem: { ...item, active: newStatus },
        //   };
        // });
        await _callClientAction(
          (onChangeEventHandler as ActionConfig[]).map((cfg) => {
            return {
              ...cfg,
              param: {
                ...item,
                active: newStatus,
                prev_active: item?.active,
              },
            };
          })
        );
      }
    }

    // submitOnChange && submitChange({ key: tenantName, value: state });
    submitOnChange && submit && submit({ ...item, ...state, value });
  };
  return (
    <ScratchPadWrapper
      title={title}
      transparentBackground={transparentBackground}
    >
      <>
        {items.map((item: U, idx: number) => {
          const currentValue = item[itemValueProp];
          return (
            <Controller
              key={idx}
              control={item.control}
              name={item.name}
              rules={item.rules}
              defaultValue={currentValue}
              render={({ field: { value, onChange } }) => (
                <ActivationButton
                  {...item}
                  label={item[itemLabelProp]}
                  key={idx}
                  name={item.name}
                  bottomDivider={divider}
                  onChange={(state) => {
                    handleChange(item, state, onChange, value);
                  }}
                  currentState={value}
                  states={states}
                  valueProp={valueProp}
                  nameProp={nameProp}
                />
              )}
            />
          );
        })}
      </>
    </ScratchPadWrapper>
  );
}
