import React, { useCallback, useState, useEffect, useMemo } from 'react';
import {
  StateShell,
} from '../../../../Shells';
import { Pagination } from '../../../../Fragments/Pagination/Pagination';
import styled from 'styled-components';
import { ScrollableContent } from '../../../../Components/Scrollbar/Scrollbar';
import { useListPagination2 } from '../../../../Components';
import {  ClaimCardList } from '../../../../Fragments';
import { StaffMember } from '../../../../Auth';
import { AllInfo, StateConfig } from '../../../../Engine';
import { useFiltersAndSearchStore } from '../../../../store/useFiltersAndSearchStore';




const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const FiltersWrapper = styled.div`
  position: relative;
  z-index: 2;
`;

const JobListWrapper = styled.div`
  position: relative;
  z-index: 1;
  margin-top: 1rem;
`;

const ViewShellPagination = styled(Pagination)`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
`;

const Content = styled(ScrollableContent)<{
  items?: any[];
  scrollable?: boolean;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  height: calc(100vh - 2rem - 72px);

  ${(props) =>
    props?.scrollable || (props?.items && props?.items.length > 0)
      ? 'padding-bottom: 4rem; box-sizing: border-box'
      : ''};
  ${(props) => (!props?.scrollable ? 'overflow: hidden;' : '')};
`;



interface Props {
  claims: any[];
  staffMember: StaffMember;
  allInfo: Partial<AllInfo>;
  itemsPerPage?: number;
  jobCardNumberPrefix: string;
  searchUrl?: string;
  token?: string;
  tokenPrefix?: string;
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  actionPanels: StateConfig['actionPanels'];
  // tokenPrefix?: string;
  // token?: string;
  getClaimMenuItems: (claim: any) => {icon: string; label: string; path: string}[];
  getJobMenuItems: (job: any) => {icon: string; label: string; path: string}[];
}

export function ClaimsDetailedView({ ClaimLinkRouter, searchUrl, token, tokenPrefix, JobLinkRouter, claims, actionPanels, staffMember, allInfo, itemsPerPage, getClaimMenuItems, getJobMenuItems}: Props) {
  // Initialize the filters and search store
  const filtersAndSearchStore = useFiltersAndSearchStore();
  
  // Create a stable config object that only changes when the dependencies change
  const storeConfig = useMemo(() => ({
    items: claims || [],
    searchUrl,
    token,
    tokenPrefix,
  }), [claims, searchUrl, token, tokenPrefix]);
  
  // Setup the store with initial values
  useEffect(() => {
    filtersAndSearchStore.setup(storeConfig);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storeConfig]); // Only re-run when storeConfig changes
  
  // Use filtered items from the store if available, otherwise use original claims
  const filteredClaims = filtersAndSearchStore.currentItems.length > 0 ? 
    filtersAndSearchStore.currentItems : 
    claims || [];
  
  const { pages, currentPage, pageItems, ...rest } = useListPagination2({
    items: filteredClaims,
    itemsPerPage: itemsPerPage || 10,
  });

  const config: StateConfig = {
    title: { 
        template: '', 
        
    },
    fetchCalls: [],
    onEnter: [],
    onLeave: [],
    defaultScreen: '',
    screens: {},
    actionPanels,
};
  
  return (
      <StateShell
        callClientAction={() => {}}
        stateConfig={config}
        clientDataObject={{}}
        fakeUseNavigation={{ state: 'idle' }}
      >
        <ModuleContent data-testid="workflow-view-shell-module-content">
          <Content
            data-testid="workflow-view-shell-content"
            items={pageItems}
            scrollable={true}
          >
            {/* <FiltersWrapper>
              <DefaultWorkflowFilters 
                filtersData={[]}
                filterMenuData={[]}
                items={claims || []}
                // The store now handles filtered items internally
                setFilteredItems={(items: any[]) => filtersAndSearchStore.setCurrentItems(items)}
              />
            </FiltersWrapper> */}
            <JobListWrapper>
              <ClaimCardList claims={pageItems} staffMember={staffMember} allInfo={allInfo} getClaimMenuItems={getClaimMenuItems} getJobMenuItems={getJobMenuItems} ClaimLinkRouter={ClaimLinkRouter} JobLinkRouter={JobLinkRouter} />
            </JobListWrapper>
          </Content>
          <ViewShellPagination
            pages={pages}
            currentPage={currentPage}
            {...rest}
          />
        </ModuleContent>
      </StateShell>
  );
}
