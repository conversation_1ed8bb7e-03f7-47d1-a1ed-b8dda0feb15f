import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FilterSelectorItem } from '../../../../Components/Filter/Filter';
import { WorkFlowFilterData } from '../../../../Components/Filter/WorkFlowFilter/WorkFlowFilter';
import { ClaimsDetailedView } from './ClaimsDetailedView';
import { sampleStaffMember } from '../../JobsOnly/JobsDetailedView/sample-staff-member';
import { sampleAllInfo } from '../../JobsOnly/JobsDetailedView/sample-all-info';

const meta: Meta<typeof ClaimsDetailedView> = {
  component: ClaimsDetailedView,
  title: 'Screens/WorkflowModule/ClaimsAndJobs/ClaimsDetailedView',
  decorators: [(Story) => <Story />],
};
export default meta;
type Story = StoryObj<typeof ClaimsDetailedView>;

const filtersData: WorkFlowFilterData[] = [
  {
    // Basic Status Filter
    buttonText: 'State',
    items: [
      {
        text: 'State is 26',
        filterCondition: {
          name: 'State is in 26',
          key: 'state',
          value: 26,
          operator: 'equals',
        },
      },
      {
        text: 'Recent Updates',
        filterCondition: {
          name: 'recentUpdates',
          key: 'status.lastUpdated',
          value: '2025-02-20',
          operator: 'greaterThan',
        },
      },
    ],
    searchable: true,
    autoFocus: true,
  },
  // Location Filter with Multiple Levels
  {
    buttonText: 'Location',
    items: [
      {
        text: 'By State',
        items: [
          {
            text: 'California',
            filterCondition: {
              name: 'locationState',
              key: 'location.state',
              value: 'CA',
              operator: 'equals',
            },
          },
          {
            text: 'Florida',
            filterCondition: {
              name: 'locationState',
              key: 'location.state',
              value: 'CA',
              operator: 'equals',
            },
          },
          {
            text: 'Arizona',
            filterCondition: {
              name: 'locationState',
              key: 'location.state',
              value: 'CA',
              operator: 'equals',
            },
          },
          {
            text: 'New York',
            filterCondition: {
              name: 'locationState',
              key: 'location.state',
              value: 'CA',
              operator: 'equals',
            },
          },
        ],
      },
      {
        text: 'By ZIP',
        items: [
          {
            text: 'SF Area',
            filterCondition: {
              name: 'locationZip',
              key: 'location.address.zip',
              value: '94',
              operator: 'startsWith',
            },
          },
          {
            text: 'AK Area',
            filterCondition: {
              name: 'locationZip',
              key: 'location.address.zip',
              value: '94',
              operator: 'startsWith',
            },
          },
          {
            text: 'SP Area',
            filterCondition: {
              name: 'locationZip',
              key: 'location.address.zip',
              value: '94',
              operator: 'startsWith',
            },
          },
        ],
      },
    ],
    searchable: true,
    autoFocus: true,
  },
  // Experience and Skills Filter
  {
    buttonText: 'Requirements',
    items: [
      {
        text: 'Experience Level',
        items: [
          {
            text: '5+ Years',
            filterCondition: {
              name: 'seniorLevel',
              key: 'requirements.experience.years',
              value: 5,
              operator: 'greaterThanOrEqual',
            },
          },
        ],
      },
      {
        text: 'Skills',
        items: [
          {
            text: 'React Developer',
            filterCondition: {
              name: 'reactSkill',
              key: 'requirements.skills',
              value: 'react',
              operator: 'hasAny', // Array contains operation
            },
          },
        ],
      },
    ],
    searchable: true,
    autoFocus: true,
  },
  // Salary Range Filter
  {
    buttonText: 'Compensation',
    items: [
      {
        text: 'Salary Range',
        items: [
          {
            text: '$120k - $150k',
            filterCondition: {
              name: 'salaryRange',
              key: 'compensation.salary.min',
              value: 120000,
              secondValue: 150000,
              operator: 'between',
            },
          },
        ],
      },
      {
        text: 'Benefits',
        items: [
          {
            text: 'Has Health Insurance',
            filterCondition: {
              name: 'healthBenefit',
              key: 'compensation.benefits',
              value: ['health'],
              operator: 'hasAll', // Must have all specified benefits
            },
          },
        ],
      },
    ],
    searchable: true,
    autoFocus: true,
  },
  // Company Details Filter with OR Logic Group
  {
    buttonText: 'Company',
    items: [
      {
        text: 'Large Tech Companies',
        filterCondition: {
          name: 'largeTechCompany',
          key: 'company.details',
          value: { size: 'large', industry: 'software' },
          operator: 'equals',
          group: 'companyType', // Using groups for OR logic
        },
      },
      {
        text: 'Medium Tech Companies',
        filterCondition: {
          name: 'mediumTechCompany',
          key: 'company.details',
          value: { size: 'medium', industry: 'software' },
          operator: 'equals',
          group: 'companyType', // Same group for OR logic
        },
      },
    ],
    searchable: true,
    autoFocus: true,
  },
];

const filterMenuData: FilterSelectorItem[] = [
  {
    text: 'Active Jobs',
    filterCondition: {
      name: 'activeStatus',
      key: 'status.current',
      value: 'active',
      operator: 'equals',
    },
  },
  {
    text: 'Recent Updates',
    filterCondition: {
      name: 'recentUpdates',
      key: 'status.lastUpdated',
      value: '2025-02-20',
      operator: 'greaterThan',
    },
  },
  {
    text: 'By Location',
    items: [
      {
        text: 'California',
        filterCondition: {
          name: 'locationState',
          key: 'location.state',
          value: 'CA',
          operator: 'equals',
        },
      },
      {
        text: 'New York',
        filterCondition: {
          name: 'locationState',
          key: 'location.state',
          value: 'NY',
          operator: 'equals',
        },
      },
    ],
  },
];

const StyledLink = styled(Link)`
  text-decoration: none; /* Remove underline */
  color: inherit; /* Inherit text color from parent */
  margin: 0; /* Remove margin */
  padding: 0; /* Remove padding*/
  display: flex; /* Ensure it behaves like an inline element */

  transition: color 0.3s ease; /* Smooth color transition */

  & :hover {
    background-color: #555758; /* Slightly lighter gray on hover */
    color: #ffffff;
  }

  && .job-card-dropdown:hover {
    background-color: rgba(40, 48, 51, 0.93);
  }
`;

const ClaimLinkRouter = ({ children, claim }: { children: any; claim: any }) => {
  return claim.permissionGranted ? (
    <StyledLink to={`/claim-flow/${claim.id}/${claim.state}`}>{children}</StyledLink>
  ) : (
    <>{children}</>
  );
};

const JobLinkRouter = ({ children, job }: { children: any; job: any }) => {
  return job.permissionGranted ? (
    <StyledLink to={`/job-flow/${job.id}/${job.state}`}>{children}</StyledLink>
  ) : (
    <>{children}</>
  );
};

export const Overview: Story = {
  args: {
    claims: [],
    jobCardNumberPrefix: 'MID',
    staffMember: sampleStaffMember,
    allInfo: sampleAllInfo,
    ClaimLinkRouter,
    JobLinkRouter,
    actionPanels: [
      {
        icon: "search-sm",
        title: "Search",
    
        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: "WorkflowActionPanelSearch",
            layout: {},
            props: {},
          },
        ],
        actionLevel: "topControls",
      },
      {
        icon: "close",
        title: "Filters",
    
        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: "WorkflowActionPanelFilters",
            layout: {},
            props: {
              filters: [
                {
                  id: 'job_value',
                  name: 'Job Value',
                  type: 'range',
                },
                {
                  id: 'job_state',
                  name: 'Job State',
                  type: 'select',
                  options: [
                    { id: 'new', name: 'New', count: 12 },
                    { id: 'in_progress', name: 'In Progress', count: 8 },
                    { id: 'completed', name: 'Completed', count: 24 },
                    { id: 'on_hold', name: 'On Hold', count: 3 },
                    { id: 'cancelled', name: 'Cancelled', count: 5 },
                  ],
                },
                {
                  id: 'job_type',
                  name: 'Job Type',
                  type: 'select',
                  options: [
                    { id: 'repair', name: 'Repair', count: 18 },
                    { id: 'installation', name: 'Installation', count: 15 },
                    { id: 'maintenance', name: 'Maintenance', count: 22 },
                    { id: 'inspection', name: 'Inspection', count: 7 },
                  ],
                },
                {
                  id: 'customer_type',
                  name: 'Customer Type',
                  type: 'select',
                  options: [
                    { id: 'residential', name: 'Residential', count: 35 },
                    { id: 'commercial', name: 'Commercial', count: 27 },
                    { id: 'government', name: 'Government', count: 8 },
                  ],
                },
              ],
              label: "Filter Workflow",
            },
          }
        ],
        actionLevel: "topControls",
      },
      {
        icon: "building-04",
        title: "Buckets",
    
        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: "WorkflowActionPanelBuckets",
            layout: {},
            props: {
              buckets: [
                {
                  id: 'photos',
                  name: 'Photos',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'photo',
                    operator: 'equals'
                  }
                },
                {
                  id: 'quotations',
                  name: 'Quotations',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'invoice',
                    operator: 'equals'
                  }
                },
                {
                  id: 'invoices',
                  name: 'Invoices',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'report',
                    operator: 'equals'
                  }
                },
                {
                  id: 'reports',
                  name: 'Reports',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'invoice',
                    operator: 'equals'
                  }
                },
                {
                  id: 'latestDocuments',
                  name: 'Latest Documents',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'invoice',
                    operator: 'equals'
                  }
                },
              ],
              label: "Bucket Filter",
            }
          }
        ],
        actionLevel: "topControls",
      },
        {
          icon: 'bell-02',
          title: 'Messages', //?actionPanel=Messages--bell-02
    
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'NoteCardList',
              layout: {
                marginTop: '20px',
                marginLeft: '10px',
                marginRight: '10px',
              },
              props: {
                notes: [
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                ],
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
        {
          icon: 'trash-01',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
    
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'NoteCardList',
              layout: {
                marginTop: '20px',
                marginBottom: '20px',
                marginLeft: '10px',
                marginRight: '10px',
              },
              props: {
                notes: [
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                ],
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ]
  },
};
