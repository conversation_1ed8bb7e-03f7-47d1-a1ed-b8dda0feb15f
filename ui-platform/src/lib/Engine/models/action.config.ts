import { NavigateOptions, SubmitOptions } from 'react-router-dom';
// import { ModalConfig } from './modal-config';
import { FetcherSubmitOptions } from 'react-router-dom/dist/dom';
import { FetchConfig } from './fetch.config';

// export interface ActionConfig {
//     type: 'clientAction' | 'serverAction';
//     action: string;
//     payload: any;
// }

type BaseActionConfig =
  | NavigateActionConfig
  | ConsoleLogActionConfig
  | LogoutActionConfig
  | TriggerModalActionConfig
  | SubmitActionConfig
  | fetchActionConfig
  | DefaultModalActionConfig
  | ResetFieldActionConfig
  | SubmitAsyncConfig
  | CloseModalActionConfig
  | ConditionalActionConfig
  | ClearStoreActionConfig
  | ClearErrorsActionConfig
  | TriggerFetchCallConfig
  | UpdateStoreActionConfig
  | TimeoutActionConfig
  | ViewJobCardPDFActionConfig
  | SwitchActionConfig
  | ForEachActionConfig;

type OptionalBase = {
  param?: any;
  debounce?: number;
  debug?: boolean;
  ignoreParam?: boolean;
};

type NavigateActionConfig = {
  type: 'clientAction';
  action: 'navigate';
  payload: [string] | [string, Record<string, any>];
} & OptionalBase;

type ConsoleLogActionConfig = {
  type: 'clientAction';
  action: 'log';
  payload: any[];
} & OptionalBase;

type LogoutActionConfig = {
  type: 'clientAction';
  action: 'logout';
  payload: any[];
} & OptionalBase;

type TriggerModalActionConfig = {
  type: 'clientAction';
  action: 'triggerModal';
  payload: [any];
} & OptionalBase;

type SubmitActionConfig = {
  type: 'clientAction';
  action: 'submitAndNavigate';
  payload: [
    { [key: string]: any },
    {
      url: string;
      slicePath?: string;
      bodySlicePath?: string;
      headers?: { [key: string]: any };
      redirect: string;
    },
    SubmitOptions
  ];
} & OptionalBase;

type fetchActionConfig = {
  type: 'clientAction';
  action: 'submitAndFetch';
  payload: [
    { [key: string]: any },
    {
      url: string;
      slicePath?: string;
      bodySlicePath?: string;
      headers?: { [key: string]: any };
      loader?: string;
    },
    FetcherSubmitOptions
  ];
} & OptionalBase;

type SubmitAsyncConfig = {
  type: 'clientAction';
  action: 'submitAsync';
  payload: {
    calls: Call[];
    onFinish?: ActionConfig;
    redirect?: string;
  };
} & OptionalBase;

type TriggerFetchCallConfig = {
  type: 'clientAction';
  action: 'triggerFetchCall';
  payload: FetchConfig[];
} & OptionalBase;

type DefaultModalActionConfig = {
  type: 'clientAction';
  action: 'defaultModalAction';
  payload: any; // No payload needed for closing the modal
} & OptionalBase;

type Call = {
  key: string;
  data?: any;
  // file?: File;
  url: string;
} & OptionalBase;

type ResetFieldActionConfig = {
  type: 'clientAction';
  action: 'resetFields';
  payload: { fields: string[] | { fieldName: string; defaultValue: string }[] }; // No payload needed for closing the modal
} & OptionalBase;

type CloseModalActionConfig = {
  type: 'clientAction';
  action: 'closeModal';
  payload?: any; // No payload needed for closing the modal
} & OptionalBase;

type ConditionalActionConfig = {
  type: 'clientAction';
  action: 'conditional';
  payload: {
    condition: string | boolean;
    actions: { whenTrue: ActionConfig[]; whenFalse?: ActionConfig[] };
  };
} & OptionalBase;

type ClearStoreActionConfig = {
  type: 'clientAction';
  action: 'clearStore';
  payload: string[];
} & OptionalBase;

type UpdateStoreActionConfig = {
  type: 'clientAction';
  action: 'updateStore';
  payload: Array<Record<string, any>>;
} & OptionalBase;

type ClearErrorsActionConfig = {
  type: 'clientAction';
  action: 'clearErrors';
  payload?: string;
} & OptionalBase;

type TimeoutActionConfig = {
  type: 'clientAction';
  action: 'timeout';
  payload: [number, ActionConfig[]];
} & OptionalBase;

type ViewJobCardPDFActionConfig = {
  type: 'clientAction';
  action: 'viewJobCardPDF';
  payload: {
    jobId: string;
    apiBaseUrl: string;
    pdfUrl: string;
  };
} & OptionalBase;

export type SwitchCase = {
  caseName: string | number | boolean;
  actions: ActionConfig[];
} & OptionalBase;

type SwitchActionConfig = {
  type: 'clientAction';
  action: 'switch';
  payload: {
    value?: string | number | boolean;
    pathToValue?: string;
    cases: SwitchCase[];
  };
} & OptionalBase;

type ForEachActionConfig = {
  type: 'clientAction';
  action: 'forEach';
  payload: {
    value?: any[];
    pathToValue?: string[];
    actions: ActionConfig[];
  };
} & OptionalBase;
/**
 * Extended ActionConfig type that includes an optional async property
 * to specify whether the action should be executed asynchronously
 */
export type ExtendedActionConfig = BaseActionConfig & {
  /**
   * When true, the action will be executed asynchronously and return a Promise
   * When false or undefined, the action will be executed synchronously (default behavior)
   */
  async?: boolean;
  concurrencyLimit?: number;
  debug?: boolean;
  asyncLoadStart?: boolean;
  asyncLoadEnd?: boolean;
};

export type ActionConfig = BaseActionConfig | ExtendedActionConfig;
