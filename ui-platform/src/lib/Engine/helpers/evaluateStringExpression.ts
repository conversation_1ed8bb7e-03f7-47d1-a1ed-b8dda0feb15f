import { renderTemplateWithJS } from 'js-in-strings';
import { getNestedProperty } from './client-utils';
import { parseAndExecuteQuery } from './query-utils';
import { renderTemplate } from './render-template';
import { templateFunctions } from './render-template-functions';

export const evalStringExpression = (value: string, obj: any) => {
  let val;
  if (value.startsWith('$')) {
    // Existing store path logic
    const [storePath, query] = value.replace('$', '').split('?');
    val = getNestedProperty(obj, storePath, '');

    if (query && Array.isArray(val)) {
      try {
        val = parseAndExecuteQuery(val, query);
      } catch (error) {
        console.error(`Error executing query for ${storePath}:`, error);
      }
    }
  } else if (value.startsWith('#')) {
    // New template string processing with # prefix
    val = renderTemplate(
      value.substring(1),
      obj,
      templateFunctions(obj, obj?.formDataRaw)
    );
  } else if (value.startsWith('js:')) {
    // New template string processing with js: prefix
    const template = value.substring(3);
    val = renderTemplateWithJS(template, obj, {
      returnRawValues: true,
      sandbox: true,
    });
  } else {
    val = value;
  }
  return val;
};
