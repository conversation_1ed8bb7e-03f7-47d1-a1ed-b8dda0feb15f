import { create } from 'zustand';

// Define a type for filter functions
export type FilterFunction = (items: any[]) => any[];

interface FiltersAndSearchState {
  // State
  searchTerm: string;
  isSearching: boolean;
  items: any[];
  currentItems: any[];
  filterFunctions: { id: string; filterFn: FilterFunction }[];
  searchUrl?: string;
  token?: string;
  tokenPrefix?: string;
  
  // Actions
  setSearchTerm: (term: string) => void;
  performSearch: (term: string, isClosedClaim: boolean) => Promise<void>;
  handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSearchSubmit: (term: string, isClosedClaim: boolean) => void;
  addFilterFunction: (id: string, filterFn: FilterFunction) => void;
  removeFilterFunction: (id: string) => void;
  clearFilters: () => void;
  applyFilters: () => void;
  setCurrentItems: (items: any[]) => void;
  
  // Optional functions
  sortItemsBy?: (key: string, order?: 'asc' | 'desc') => any;
  
  // Setup function - to be called once when initializing the store with component-specific values
  setup: (params: {
    items: any[];
    searchUrl?: string;
    token?: string;
    tokenPrefix?: string;
    sortItemsBy?: (key: string, order?: 'asc' | 'desc') => any;
  }) => void;
}

// For managing abort controller references
let abortControllerRef: AbortController | null = null;
let latestSearchId = 0;

export const useFiltersAndSearchStore = create<FiltersAndSearchState>((set, get) => ({
  // Initial state
  searchTerm: '',
  isSearching: false,
  items: [],
  currentItems: [],
  filterFunctions: [],
  searchUrl: '',
  token: '',
  tokenPrefix: '',
  sortItemsBy: undefined,
  
  // Setup function to initialize the store with component-specific values
  setup: (params) => {
    const { items, searchUrl, token, tokenPrefix, sortItemsBy } = params;
    set({ 
      items, 
      currentItems: items, 
      searchUrl, 
      token, 
      tokenPrefix, 
      sortItemsBy 
    });
    
  },
  
  // Actions
  setSearchTerm: (term) => set({ searchTerm: term }),
  
  performSearch: async (term, isClosedClaim) => {
    const { searchUrl, items, token, tokenPrefix, applyFilters } = get();
    const searchId = Date.now(); // Generate unique ID for this search request
    latestSearchId = searchId;
    
    if (!term || !searchUrl) {
      set({ isSearching: false });
      // Apply any active filters to the items
      applyFilters();
      return;
    }
    
    set({ isSearching: true });
    try {
      // Cancel any ongoing search request
      if (abortControllerRef) {
        abortControllerRef.abort();
      }
      
      // Create a new abort controller for this request
      const abortController = new AbortController();
      abortControllerRef = abortController;
      
      const response = await fetch(`${searchUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          authorization: `${tokenPrefix} ${token}`,
        },
        body: JSON.stringify({
          search: term,
          active: !isClosedClaim,
          searchId, // Include searchId to track request
        }),
        signal: abortController.signal,
      });
      
      if (!response.ok) throw new Error('Search request failed');
      const data = await response.json();
      const payload = data.payload || [];
      
      // Only update if this is still the latest search
      if (searchId === latestSearchId) {
        set({ items: payload }); // Update the base items
        const { applyFilters } = get();
        applyFilters(); // Apply filters to the new items
      }
    } catch (error) {
      if ((error as any)?.name === 'AbortError') return; // build type error fix
      console.error('Search error:', error);
      const { applyFilters } = get();
      applyFilters(); // Apply filters even on error
    } finally {
      set({ isSearching: false });
    }
  },
  
  handleSearchChange: (event) => {
    event.preventDefault();
    const term = event.target.value;
    set({ searchTerm: term });
    // No longer triggering search on change, only updating the search term
  },
  
  handleSearchSubmit: (term, isClosedClaim) => {
    const { performSearch } = get();
    performSearch(term, isClosedClaim);
  },
  
  // Add a filter function with a unique ID
  addFilterFunction: (id, filterFn) => {
    const { filterFunctions, applyFilters } = get();
    // Remove any existing filter with the same ID
    const updatedFilters = filterFunctions.filter(filter => filter.id !== id);
    // Add the new filter function
    set({ filterFunctions: [...updatedFilters, { id, filterFn }] });
    // Apply all filters
    applyFilters();
  },
  
  // Remove a filter function by ID
  removeFilterFunction: (id) => {
    const { filterFunctions, applyFilters } = get();
    set({ filterFunctions: filterFunctions.filter(filter => filter.id !== id) });
    // Re-apply remaining filters
    applyFilters();
  },
  
  // Apply all filter functions to the items
  applyFilters: () => {
    const { items, filterFunctions } = get();
    
    console.log('Applying filters:', filterFunctions.length, 'filter functions');
    console.log('Original items:', items.length);
    
    if (items.length > 0) {
      console.log('Sample item structure:', JSON.stringify(items[0], null, 2));
    }
    
    // Start with the original items
    let filteredItems = [...items];
    
    // Apply each filter function in sequence
    filterFunctions.forEach(({ id, filterFn }, index) => {
      const beforeCount = filteredItems.length;
      filteredItems = filterFn(filteredItems);
      const afterCount = filteredItems.length;
      
      console.log(`Filter ${index} (${id}): ${beforeCount} items → ${afterCount} items`);
    });
    
    console.log('Final filtered items:', filteredItems.length);
    
    // Update the currentItems with the filtered results
    set({ currentItems: filteredItems });
  },
  
  clearFilters: () => {
    const { items } = get();
    set({ 
      searchTerm: '',
      filterFunctions: [] 
    });
    // Reset currentItems to original items
    set({ currentItems: items });
  },
  
  setCurrentItems: (items) => set({ currentItems: items }),
}));

// Create a hook that mimics the original useFiltersAndSearch API
export function useFiltersAndSearch() {
  return useFiltersAndSearchStore();
}
