import React from 'react';
import styled from 'styled-components';
import { B64StringLogo } from '../../ClientLogos/DynamicB64StringLogo';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';
import useViewportDevice from '../../../Hooks/useViewportDevice';
import { ButtonContextMenu } from '../../ContextMenu';
import { Icon } from '../../Icons';

interface ClaimCardProps {
  claim?: any;
  ClaimLinkRouter: any;
  displayLogo?: boolean;
}

const ClaimCardContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  > :first-child {
  }
  @media (min-width: 1200px) {
    grid-template-columns: 1.6fr 2.2fr;
  }
  @media (min-width: 768px) and (max-width: 1200px) {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 0.3fr 1fr;
    > :first-child {
      width: 100%;
    }
  }
  @media (max-width: 768px) {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 0.3fr 1fr;
    > :first-child {
      display: grid;
      max-height: 100px;
      text-align: center;
    }
  }
  line-height: 1.5;
`;

const ClaimCardContent = styled.div`
  display: grid;
  grid-template-columns: 0.1fr 1fr 1fr;
  padding: 25px 17px 25px 0;
  line-height: 1.5;
  > :nth-child(2) {
    align-self: center;
  }
  > :nth-child() {
    min-width: 175px;
  }
  > :first-child {
    align-self: center;
  }
  @media (max-width: 1200px) and (min-width: 768px) {
    border-top: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  }
  @media (min-width: 1200px) {
    border-left: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  }
  @media (max-width: 768px) {
    padding: 25px 17px 25px 0;
    border-top: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
    grid-template-columns: 0.1fr 1.5fr 3.5fr;
  }
  @media (max-width: 414px) {
    padding: 25px 12px 25px 0;
    grid-template-columns: 0.1fr 1.5fr 3.5fr;
  }
`;

const ClaimCardDetails = styled.div`
  display: grid;
  grid-auto-rows: min-content;
  align-self: top;
  line-height: 1.5;
  grid-gap: 2px;
  font-size: smaller;
  text-align: left;
  padding-left: 17px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
`;

const JobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.8rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
`;

const ClaimCardActions = styled.div`
  align-self: center;
`;

export const ClaimSection: React.FC<ClaimCardProps> = ({
  claim,
  ClaimLinkRouter,
  displayLogo,
}) => {
  const { isMobile } = useViewportDevice();
  const logoSource = claim?.jobs?.[0]?.source || claim.source;

  if (isMobile) {
    return (
      <ClaimCardContainer>
        <ClaimLinkRouter claim={claim}>
          <ClaimCardContent>
            {claim?.permissionGranted && (
              <PermissionsIndicator color={'blue'} size="" position="" />
            )}
            <ClaimCardDetails>
              <div style={{ fontWeight: 'bold' }}>{claim?.mid}</div>
              <div>{claim?.formattedDate}</div>
              <JobCardText>{claim?.customer}</JobCardText>
              <div>{claim?.stateTextDisplay}</div>
              <div>State</div>
              <div style={{ color: 'red' }}>1d 01h 37m</div>
            </ClaimCardDetails>
          </ClaimCardContent>
        </ClaimLinkRouter>
      </ClaimCardContainer>
    );
  }

  return (
    <ClaimCardContainer>
      <ClaimLinkRouter claim={claim}>
        {logoSource && displayLogo && (
          <B64StringLogo
            id_string={logoSource}
            maximumWidth={100}
            minimumWidth={135}
          />
        )}
        <ClaimCardContent>
          {claim?.permissionGranted && (
            <PermissionsIndicator color={'blue'} size="" position="" />
          )}
          <ClaimCardDetails>
            <div style={{ fontWeight: 'bold' }}>{claim?.mid}</div>
            <div>{claim?.formattedDate}</div>
            <JobCardText>{claim?.customer}</JobCardText>
            <div>{claim?.stateTextDisplay}</div>
            <div>State</div>
            <div style={{ color: 'red' }}>1d 01h 37m</div>
          </ClaimCardDetails>
        </ClaimCardContent>
      </ClaimLinkRouter>
    </ClaimCardContainer>
  );
};