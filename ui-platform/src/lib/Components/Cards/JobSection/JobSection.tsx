import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';
import { ButtonContextMenu, useContextMenuOptions } from '../../ContextMenu';
import { Icon } from '../../Icons/Icon';
import { MenuItemConfig } from '../../../Engine/models/menu-list-item.config';
import { withItems } from '../../List/List';
import { MenuItem } from '../../Menu';
import useViewportDevice from '../../../Hooks/useViewportDevice';

interface ClaimCardProps {
  claim?: any;
  JobLinkRouter: any;
  menuItems: { icon: string; label: string; path: string }[];
}

const JobCardContainer = styled.div`
  display: grid;
  grid-template-rows: auto;
  border-left: solid 1px ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  > :first-child {
    border-top-right-radius: 4px;
  }
  > div:last-child {
    border-bottom: none;
    border-bottom-right-radius: 4px;
    align-self: center;
  }
  @media (max-width: 414px) {
    display: grid;
    width: 100%;
    grid-template-rows: auto;
    > :first-child {
      border-top-right-radius: 0px;
    }
    > :last-child {
      border-bottom-right-radius: 0px;
      border: none;
      align-self: center;
    }
  }
`;

const JobCard = styled.div`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.1fr 1.3fr 1fr 1.4fr 1fr 0.3fr;
  padding: 0 0 0 0;
  height: auto;
  align-items: center;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  > :last-child {
    border-bottom: none;
    align-self: center;
  }
  @media(min-width: 1200px) {
    grid-template-columns: 0.1fr 1fr 1fr 1.6fr 0.5fr 0.5fr;
    > :nth-child(6) {
      align-self: center;
      justify-self: center;
    }
  }
  @media (max-width: 1199px) {
    grid-template-columns: 0.1fr 1fr 1fr 1fr 1fr 0.5fr;
  }
  @media (max-width: 768px) {
    display: grid;
    width: 100%;
    max-width: 768px;
    grid-template-columns: 1fr;
    > :nth-child(n) {
      padding: 5px 0 5px 15px;
    }
  }
`;

const MobileJobCard = styled.div`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.1fr 1.5fr 1fr 1fr;
  padding: 10px;
  height: auto;
  align-items: center;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
`;

const JobCardIndicator = styled.div`
  align-content: center;
  justify-self: flex-start;
  width: 6px;
  height: 100%;
`;

const JobCardSection = styled.div`
  padding: 15px 0 15px 15px;
  line-height: 1.5;
  @media (max-width: 1560px) {
    max-width: 200px;
  }
  @media (max-width: 768px) {
    max-width: 120px;
  }
`;

const MobileJobcardSection = styled.div`
  display: grid;
  grid-template-columns: 0.1fr 0.2fr 1fr;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  padding: 10px 0 10px 0;
  align-self: center;
`;

const JobCardNotesAndCMenuSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;

  > :first-child {
    justify-self: end;
    padding-right: 10px;
  }

  > :last-child {
    justify-self: start;
    padding-left: 10px;
  }

  @media (min-width: 768px) and (max-width: 1200px) {
    > :last-child {
      padding-right: 17px;
    }
  }
  @media (max-width: 768px) {
    > :last-child {
      padding-left: 22px;
    }
  }
`;

const JobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.8rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
  @media (max-width: 414px) {
    font-size: 0.7rem;
  }
`;

const WithMultipleItems = ({ menuItems, navigate, setShowMenu }: any) => {
  const items: MenuItemConfig[] = menuItems?.map((item: any) => ({
    icon: item.icon,
    label: item.label,
    onClick: (event: React.MouseEvent) => {
      event.stopPropagation();
      if (item.path) {
        navigate(item.path);
      }
      if (setShowMenu) {
        setShowMenu(false);
      }
    },
  }));
  return withItems(MenuItem)(items);
};

export const JobSection: React.FC<ClaimCardProps> = ({
  claim,
  JobLinkRouter,
  menuItems,
}) => {
  const navigate = useNavigate();
  const { showMenu, setShowMenu } = useContextMenuOptions();
  const { isMobile } = useViewportDevice();

  if (isMobile) {
    return (
      <JobCardContainer>
        {claim?.jobs?.map((job: any, index: number) => (
          <JobLinkRouter job={job} key={job.id}>
            <MobileJobCard key={index}>
              <JobCardIndicator>
                {job?.permissionGranted && (
                  <PermissionsIndicator color={'green'} size="" position="" />
                )}
              </JobCardIndicator>
              <JobCardSection>
                <JobCardText>{job.skillName || job.skill}</JobCardText>
                <JobCardText>{job.teamleadName || job.sp}</JobCardText>
              </JobCardSection>
              <JobCardSection>
                <JobCardText>
                  <div>{job.formattedDate || job.date}</div>
                </JobCardText>
                <JobCardText>
                  <div>{job.time}</div>
                </JobCardText>
              </JobCardSection>
              <JobCardNotesAndCMenuSection>
                <ButtonContextMenu
                  children={<WithMultipleItems menuItems={menuItems} navigate={navigate} setShowMenu={setShowMenu} />}
                  orientation="right"
                  additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                  dropdownClassName="job-card-dropdown"
                />
              </JobCardNotesAndCMenuSection>
            </MobileJobCard>
          </JobLinkRouter>
        ))}
      </JobCardContainer>
    );
  }

  return (
    <JobCardContainer>
      {claim?.jobs?.map((job: any, index: number) => (
        <JobLinkRouter job={job} key={job.id}>
          <JobCard key={index}>
            <JobCardIndicator>
              {job?.permissionGranted && (
                <PermissionsIndicator color={'green'} size="" position="" />
              )}
            </JobCardIndicator>
            <JobCardSection>
              <JobCardText>{job.skillName || job.skill}</JobCardText>
              <JobCardText>{job.teamleadName || job.sp}</JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>
                <div>{job.formattedDate || job.date}</div>
              </JobCardText>
              <JobCardText>
                <div>{job.time}</div>
              </JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>{job.state && `${job.state}: ${job.stateTextDisplay}`}</JobCardText>
              <JobCardText>{job.description}</JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>
                <div>{job.customer}</div>
                <div style={{ color: 'red', fontWeight: 'bold' }}>{job.sla}</div>
              </JobCardText>
            </JobCardSection>
            <JobCardNotesAndCMenuSection>
              <div>
                <Icon type="notes" size={20} />
              </div>
              <ButtonContextMenu
                children={<WithMultipleItems menuItems={menuItems} navigate={navigate} setShowMenu={setShowMenu} />}
                orientation="right"
                additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                dropdownClassName="job-card-dropdown"
              />
            </JobCardNotesAndCMenuSection>
          </JobCard>
        </JobLinkRouter>
      ))}
    </JobCardContainer>
  );
};