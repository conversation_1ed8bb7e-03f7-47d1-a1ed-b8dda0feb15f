import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { JobSection } from './JobSection';

const meta: Meta<typeof JobSection> = {
  component: JobSection,
  title: 'Components/Cards/JobSection',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<typeof JobSection>;

const claimWithMultipleJobs = {
  id: 9898,
  applicant: {
    first_name: '<PERSON><PERSON>',
    surname: '<PERSON><PERSON>',
    id_number: '158786932',
    claimantpoliceynum: 'None',
    bondnumber: '--',
    generated: 'from maven',
    local_file: null,
    created_by: 'auto',
  },
  application_creator: null,
  application_date: '2025-04-29T12:50:46.027033',
  state: 16,
  state_change_date: '2025-04-29T12:50:46.027034',
  location: 11883,
  modified_date: '2025-04-29T13:06:01.496459',
  mid: 'E8ZXBH',
  sub_section: 1,
  jobs: [
    {
      id: 8765,
      appointment: {
        id: 11016,
        job: 8765,
        state: 1,
        range_start: '2025-04-30T12:00:00',
        range_end: null,
        appointment_type: 4,
        reason: null,
      },
      note_count: 0,
      unread_note_count: null,
      claim_type_id: 37,
      assessor_name: '',
      lat: '-26.*********',
      long: '28.001390000',
      claim: {
        id: 9898,
        mid: 'E8ZXBH',
        is_cat: false,
        cat_code: null,
        applicant: {
          first_name: 'Macc',
          surname: 'Macc',
        },
        property_city: 'RANDBURG',
        property_complex: '',
        property_complex_block: '',
        property_complex_unit_number: '',
        property_street_name: '',
        property_street_number: '',
        property_suburb: 'FERNDALE,',
      },
      source: 'Clarity',
      source_id: 1,
      source_key: 'Cla',
      suburb: 'FERNDALE, ',
      address: ', 251 251 OAK AVENUE, RANDBURG',
      postal_code: '2194',
      claim_value: null,
      mid: null,
      ping_count: 1,
      token: 'awFL5WuddftL9gLvL2YMA4QEJy6ywKKqWCxaK73yTJGAfTStkCkr7h',
      valid_job: null,
      updated: '2025-04-29T13:06:01.439628',
      on_site: null,
      distance: null,
      job_creator: null,
      skill: 51,
      sp: null,
      team_leader: null,
      area: 1,
      state: 20,
      supplier_type: 1,
      forced_location: null,
      assessor: null,
      authorizer: null,
      location: null,
    },
    {
      id: 8766,
      appointment: {
        id: 11017,
        job: 8766,
        state: 1,
        range_start: '2025-04-30T13:00:00',
        range_end: null,
        appointment_type: 4,
        reason: null,
      },
      note_count: 0,
      unread_note_count: null,
      claim_type_id: 38,
      assessor_name: '',
      lat: '-26.*********',
      long: '28.001390000',
      claim: {
        id: 9898,
        mid: 'E8ZXBH',
        is_cat: false,
        cat_code: null,
        applicant: {
          first_name: 'Macc',
          surname: 'Macc',
        },
        property_city: 'RANDBURG',
        property_complex: '',
        property_complex_block: '',
        property_complex_unit_number: '',
        property_street_name: '',
        property_street_number: '',
        property_suburb: 'FERNDALE,',
      },
      source: 'Clarity',
      source_id: 1,
      source_key: 'Cla',
      suburb: 'FERNDALE, ',
      address: ', 251 251 OAK AVENUE, RANDBURG',
      postal_code: '2194',
      claim_value: null,
      mid: null,
      ping_count: 1,
      token: 'awFL5WuddftL9gLvL2YMA4QEJy6ywKKqWCxaK73yTJGAfTStkCkr7h',
      valid_job: null,
      updated: '2025-04-29T13:07:00.000000',
      on_site: null,
      distance: null,
      job_creator: null,
      skill: 52,
      sp: null,
      team_leader: null,
      area: 1,
      state: 21,
      supplier_type: 1,
      forced_location: null,
      assessor: null,
      authorizer: null,
      location: null,
    },
  ],
  note_count: 0,
  repudiation: null,
  deceased: [],
  form_start: '2025-04-29T12:50:46.027023',
  assessor: null,
  cat_code: null,
  claim_type_id: 37,
  private_banking: 0,
  unread_note_count: null,
  source: 'Clarity',
  source_id: 1,
  province: 1,
  source_key: 'Cla',
};

const claimWithSingleJob = {
  id: 9899,
  applicant: {
    first_name: 'Jane',
    surname: 'Doe',
    id_number: '*********',
    claimantpoliceynum: 'None',
    bondnumber: '--',
    generated: 'from maven',
    local_file: null,
    created_by: 'auto',
  },
  application_creator: null,
  application_date: '2025-04-29T12:50:46.027033',
  state: 16,
  state_change_date: '2025-04-29T12:50:46.027034',
  location: 11883,
  modified_date: '2025-04-29T13:06:01.496459',
  mid: 'CLM-SINGLE-JOB',
  sub_section: 1,
  jobs: [
    {
      id: 8767,
      appointment: {
        id: 11018,
        job: 8767,
        state: 1,
        range_start: '2025-04-30T14:00:00',
        range_end: null,
        appointment_type: 4,
        reason: null,
      },
      note_count: 0,
      unread_note_count: null,
      claim_type_id: 37,
      assessor_name: '',
      lat: '-26.*********',
      long: '28.001390000',
      claim: {
        id: 9899,
        mid: 'CLM-SINGLE-JOB',
        is_cat: false,
        cat_code: null,
        applicant: {
          first_name: 'Jane',
          surname: 'Doe',
        },
        property_city: 'RANDBURG',
        property_complex: '',
        property_complex_block: '',
        property_complex_unit_number: '',
        property_street_name: '',
        property_street_number: '',
        property_suburb: 'FERNDALE,',
      },
      source: 'Clarity',
      source_id: 1,
      source_key: 'Cla',
      suburb: 'FERNDALE, ',
      address: ', 251 251 OAK AVENUE, RANDBURG',
      postal_code: '2194',
      claim_value: null,
      mid: null,
      ping_count: 1,
      token: 'awFL5WuddftL9gLvL2YMA4QEJy6ywKKqWCxaK73yTJGAfTStkCkr7h',
      valid_job: null,
      updated: '2025-04-29T13:08:00.000000',
      on_site: null,
      distance: null,
      job_creator: null,
      skill: 53,
      sp: null,
      team_leader: null,
      area: 1,
      state: 22,
      supplier_type: 1,
      forced_location: null,
      assessor: null,
      authorizer: null,
      location: null,
    },
  ],
  note_count: 0,
  repudiation: null,
  deceased: [],
  form_start: '2025-04-29T12:50:46.027023',
  assessor: null,
  cat_code: null,
  claim_type_id: 37,
  private_banking: 0,
  unread_note_count: null,
  source: 'Clarity',
  source_id: 1,
  province: 1,
  source_key: 'Cla',
};

const claimWithoutJobs = {
  id: 9900,
  applicant: {
    first_name: 'Peter',
    surname: 'Jones',
    id_number: '*********',
    claimantpoliceynum: 'None',
    bondnumber: '--',
    generated: 'from maven',
    local_file: null,
    created_by: 'auto',
  },
  application_creator: null,
  application_date: '2025-04-29T12:50:46.027033',
  state: 16,
  state_change_date: '2025-04-29T12:50:46.027034',
  location: 11883,
  modified_date: '2025-04-29T13:06:01.496459',
  mid: 'CLM-NO-JOBS',
  sub_section: 1,
  jobs: [],
  note_count: 0,
  repudiation: null,
  deceased: [],
  form_start: '2025-04-29T12:50:46.027023',
  assessor: null,
  cat_code: null,
  claim_type_id: 37,
  private_banking: 0,
  unread_note_count: null,
  source: 'Clarity',
  source_id: 1,
  province: 1,
  source_key: 'Cla',
};

const menuItems = [
  { icon: 'eye', label: 'View', path: '/view' },
  { icon: 'pencil', label: 'Edit', path: '/edit' },
];

export const SingleJob: Story = {
  args: {
    claim: claimWithSingleJob,
    JobLinkRouter: ({ children }: { children: any }) => children,
    menuItems: menuItems,
  },
};

export const MultipleJobs: Story = {
  args: {
    claim: claimWithMultipleJobs,
    JobLinkRouter: ({ children }: { children: any }) => children,
    menuItems: menuItems,
  },
};

export const NoJobs: Story = {
  args: {
    claim: claimWithoutJobs,
    JobLinkRouter: ({ children }: { children: any }) => children,
    menuItems: menuItems,
  },
};

export const NoMenuItems: Story = {
  args: {
    claim: claimWithSingleJob,
    JobLinkRouter: ({ children }: { children: any }) => children,
    menuItems: [],
  },
}; 