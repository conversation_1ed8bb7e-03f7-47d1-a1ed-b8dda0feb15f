import { evalStringExpression } from '../Engine';

export const processTemplates = (obj: any, storeObj: any): any => {
  if (typeof obj === 'string') {
    return evalStringExpression(obj, storeObj);
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => processTemplates(item, storeObj));
  }
  if (obj && typeof obj === 'object') {
    const result: any = {};
    for (const [k, v] of Object.entries(obj)) {
      result[k] = processTemplates(v, storeObj);
    }
    return result;
  }
  return obj;
};
